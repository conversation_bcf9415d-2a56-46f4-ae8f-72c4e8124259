{"name": "coos-desktop-app", "version": "1.0.0", "description": "智胜集成基于多租户的协调办公门户COOS(用户端)", "author": "智胜集成(技术研发中心)", "scripts": {"dev:development": "node --max-old-space-size=4096 build/project-build-config/index.js dev:project", "build:project": "node build/project-build-config/index.js build:project", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "lint-fix": "eslint --fix --ext .js,.vue src", "lint-fix-performance": "eslint --fix --ext .js,.vue src/third-party/performance", "ls-lint": "ls-lint", "cz": "git add . && git cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "build:dll": "webpack --config build/dll-build-config/index.js --mode=production", "pull-git": "node pull-git.js"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue}": ["eslint --fix --ext .js,.jsx,.vue src", "prettier --write ./src"]}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@eoss-design/color": "^1.0.9", "@fingerprintjs/fingerprintjs": "^4.5.1", "@imndx/v-emoji-picker": "^2.3.7", "@logicflow/core": "^1.2.26", "@logicflow/extension": "^1.2.25", "@tinymce/tinymce-vue": "^2.0.0", "@turf/turf": "^7.1.0", "aieditor": "^1.3.8", "anchorme": "^3.0.5", "axios": "0.18.1", "axios-retry": "^3.0.0", "base64-arraybuffer": "^1.0.2", "benz-amr-recorder": "^1.1.5", "commander": "^9.1.0", "core-js": "^3.31.1", "cross-spawn": "^7.0.3", "crypto-js": "^4.2.0", "detectrtc": "^1.4.1", "draggable-vue-directive": "^2.1.0", "echarts": "^5.5.0", "element-china-area-data": "^6.1.0", "element-eoss": "^2.14.3", "file-saver": "^2.0.5", "filemanager-webpack-plugin": "^8.0.0", "glob": "^10.3.10", "gojs": "^2.2.19", "highlight.js": "^11.9.0", "inquirer": "^8.2.1", "jr-qrcode": "^1.1.4", "js-base64": "^3.7.7", "js-cookie": "2.2.0", "jsencrypt": "^3.3.2", "leaflet": "^1.9.2", "leaflet.chinatmsproviders": "^3.0.6", "lodash": "^4.17.21", "long": "^5.2.3", "normalize.css": "7.0.0", "nprogress": "0.2.0", "object-assign": "^4.1.1", "path-to-regexp": "2.4.0", "print-js": "^1.6.0", "progress-bar-webpack-plugin": "^2.1.0", "push.js": "^1.0.12", "qs": "^6.11.2", "qweather-icons": "^1.6.0", "raw-loader": "^4.0.2", "resize-image": "^0.1.0", "runtime-import": "^2.2.1", "sortablejs": "^1.15.2", "svg-baker": "^1.7.0", "svg-baker-runtime": "^1.4.7", "tinymce": "^5.10.3", "tmp": "^0.2.3", "tributejs": "^5.1.3", "twemoji": "^14.0.2", "universal-emoji-parser": "^1.0.125", "uuid": "9.0.0", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-click-outside": "^1.1.0", "vue-context": "^6.0.0", "vue-cool-lightbox": "^2.7.5", "vue-cropper": "^0.6.4", "vue-dropdowns": "^1.1.2", "vue-highlightjs": "^1.3.3", "vue-i18n": "^8.24.0", "vue-infinite-loading": "^2.4.5", "vue-js-modal": "^2.0.1", "vue-loader": "15.9.3", "vue-notification": "^1.3.20", "vue-print-nb": "^1.7.5", "vue-router": "3.0.6", "vue-signature-pad": "^2.0.5", "vue-slider-component": "^3.2.24", "vue-spinner": "^1.0.4", "vue-tippy": "^4.7.2", "vue-virtual-scroll-list": "^2.3.5", "vue-visibility-change": "^1.2.1", "vue2-water-marker": "^0.0.2", "vuedraggable": "^2.24.3", "vuex": "3.1.0", "webpack": "^4.47.0", "webpack-cli": "^3.3.11", "wujie-vue2": "^1.0.22", "xlsx": "^0.18.5", "xss": "^1.0.14"}, "devDependencies": {"@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "@kangc/v-md-editor": "1.7.12", "@ls-lint/ls-lint": "^1.10.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-loader": "10.0.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "colors-console": "^1.0.3", "commitizen": "^4.2.4", "compression-webpack-plugin": "1.1.2", "connect": "3.6.6", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "6.2.2", "ghost-progress-webpack-plugin": "^0.1.0", "html-webpack-plugin": "3.2.0", "husky": "^4.3.8", "lint-staged": "^12.0.2", "moment": "^2.30.1", "prettier": "^2.4.1", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "sass-resources-loader": "^2.2.5", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "thread-loader": "^2.1.3", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.10.2", "hard-source-webpack-plugin": "^0.13.1"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}