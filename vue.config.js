'use strict';
const path = require('path');
const config = require('./src/config');
const pkg = require('./package.json');
const webpack = require('webpack');
const DllReferencePlugin = webpack.DllReferencePlugin;
/**
//添加静态资源链接到HTML中（被html插件替代，弃用）
"add-asset-html-webpack-plugin": "^6.0.0",
const AddAssetHtmlPlugin = require('add-asset-html-webpack-plugin');
 */
// dll保存目录
const dllLocalPath = 'build/dll-build-config/vendor';
const dllPath = path.resolve(process.cwd(), `./${dllLocalPath}`);
// 服务器目录路径
const publicPath = './';
function resolve(dir) {
	return path.join(__dirname, dir);
}
const name = config.title || pkg.name;
const assetsUrl = config.assetsUrl;
const serveUrl = config.serveUrl; // 服务器域名
const dllPublishPath = '/common/dll/';
const port = process.env.port || 8081;

// 读取所有的dll文件，并拼接成CDN服务链接
const glob = require('glob');
let dllArr = glob.sync(`${dllPath}/*.js`);
// 获取build之后的文件路径
// 格式化地址格式
dllArr = dllArr.map(dll => {
	// 文件基础名称
	const dllBaseName = path.basename(dll);
	return assetsUrl + dllPublishPath + dllBaseName;
});
// 项目独有的静态数据
const projectData = require('./src/store/data-source/project-data');
// 从独有的项目数据中获取cdn配置
let cdnArr =
	projectData[process.env.VUE_APP_PROJECT] && projectData[process.env.VUE_APP_PROJECT].cdn
		? projectData[process.env.VUE_APP_PROJECT].cdn
		: [];
let cdnCss = cdnArr.filter(item => {
	return /\.css/.test(item);
});
let cdnJs = cdnArr.filter(item => {
	return /\.js/.test(item);
});
// 获取所有dll打包的json映射文件
const manifestArr = glob.sync(`${dllPath}/*.json`);
// 通过html加载的cdn资源就避免每次打包把dll打进去，多端就多份
const cdn = {
	js: [...dllArr, ...cdnJs],
	css: [...cdnCss]
	// css: [assetsUrl + '/common/coos-icon/iconfont.css']
};
// 开发环境使用ali临时的，正式使用服务器上面的
if (process.env.NODE_ENV === 'development') {
	cdn.css.push('//at.alicdn.com/t/c/font_4301275_eb5qkfkbffp.css');
} else {
	cdn.css.push(assetsUrl + '/common/coos-icon/iconfont.css');
}

// 打包进度条(根据配置判断是否引入插件)
const GhostProgressWebpackPlugin = process.env.VUE_APP_BUILD_CONFIG?.includes('process')
	? require('ghost-progress-webpack-plugin').GhostProgressPlugin
	: '';
// gzip压缩(根据配置判断是否引入插件)
const CompressionPlugin = process.env.VUE_APP_BUILD_CONFIG?.includes('gzip')
	? require('compression-webpack-plugin')
	: '';
const productionGzipExtensions = /\.(js|css)(\?.*)?$/i;
/**
// cpu核心数量计算，根据配置判断是否引入插件（因为下面配置冲突，暂时注释）
let cpuLength = process.env.VUE_APP_BUILD_CONFIG?.includes('multiCpu')
	? require('os').cpus().length
	: '';
 */
// 打包分析(根据配置判断是否引入插件)
const BundleAnalyzerPlugin = process.env.VUE_APP_BUILD_CONFIG?.includes('dependencies')
	? require('webpack-bundle-analyzer').BundleAnalyzerPlugin
	: '';
module.exports = {
	transpileDependencies: ['aieditor'], // 强制 Babel 转译 aieditor
	publicPath,
	outputDir: 'dist',
	assetsDir: 'static',
	// 是否开启eslint校验
	lintOnSave: process.env.NODE_ENV === 'development',
	productionSourceMap: false,
	devServer: {
		port: port,
		open: true,
		overlay: {
			warnings: false,
			errors: true
		},
		proxy: {
			'/api/chat': {
				target: 'http://**************:7861/chat',
				// target: 'http://**************:7861/chat',
				changeOrigin: true,
				ws: true,
				pathRewrite: {
					'/api/chat': ''
				}
			},
			// 国资委测试环境-值班信息
			'/dutymanage': {
				target: ' http://sss.eoss.wisesoft.cloud',
				pathRewrite: {
					'': ''
				}
			},
			'/api': {
				target: serveUrl, // 王果 'http://**************:9999', // serveUrl,
				changeOrigin: true,
				ws: true,
				pathRewrite: {
					'/api': ''
				}
			}
		},
		disableHostCheck: true
	},
	configureWebpack: {
		name: name,
		resolve: {
			alias: {
				'@': resolve('src'),
				'@wile': resolve('src/wile-fire')
			}
		},
		plugins: [
			// new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/)
		]
	},
	chainWebpack(config) {
		config.plugin('preload').tap(() => [
			{
				rel: 'preload',
				fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
				include: 'initial'
			}
		]);
		config.plugins.delete('prefetch');
		config.module.rule('svg').exclude.add(resolve('src/icons'));
		config.module
			.rule('icons')
			.test(/\.svg$/)
			.include.add(resolve('src/icons'))
			.end()
			.use('svg-sprite-loader')
			.loader('svg-sprite-loader')
			.options({
				symbolId: 'icon-[name]'
			});
		config.plugin('html').tap(args => {
			args[0].cdn = cdn;
			return args;
		});
		// 告诉webpack抽离的依赖已经打包到dll中(只要打包就需要)
		if (process.env.NODE_ENV !== 'development') {
			manifestArr.map(manifest => {
				config
					.plugin(DllReferencePlugin)
					.use(DllReferencePlugin, [{ context: process.cwd(), manifest }]);
			});
			/**
				// 对应的dll添加到html中引入（已被html插件替代，弃用）
				config.plugin(AddAssetHtmlPlugin).use(AddAssetHtmlPlugin, [
					{
						publicPath: path.join(publicPath, './assets/dll'), // 添加到html中的路径
						outputPath: '/assets/dll', // 打包到dist中的路径
						filepath: path.join(dllPath, './libs.dll.js') // 文件来源
					}
				]);
			 */
		}
		// 组件内使用全局的sass变量(需要拆包)
		if (process.env.VUE_APP_BUILD_CONFIG?.includes('splitChunks')) {
			config
				.plugin('ScriptExtHtmlWebpackPlugin')
				.after('html')
				.use('script-ext-html-webpack-plugin', [
					{
						inline: /runtime\..*\.js$/
					}
				]);
			config.optimization.splitChunks({
				chunks: 'all',
				cacheGroups: {
					libs: {
						name: 'chunk-libs',
						test: /[\\/]node_modules[\\/]/,
						priority: 20,
						// maxSize: 500000,
						chunks: 'initial'
					},
					elementComponent: {
						name: 'chunk-elementComponent',
						test: resolve('lib/element-component'),
						priority: 15,
						chunks: 'all',
						enforce: true // 强制分离
					},
					vForm: {
						name: 'chunk-vForm',
						test: resolve('lib/vform'),
						priority: 15,
						chunks: 'all',
						enforce: true // 强制分离
					},
					imSDK: {
						name: 'chunk-imSDK',
						test: resolve('src/wile-fire/wfc'),
						priority: 15,
						// maxSize: 80000,
						chunks: 'all',
						enforce: true // 强制分离
					},
					commons: {
						name: 'chunk-commons',
						test: resolve('src/components'),
						minChunks: 3,
						priority: 5,
						reuseExistingChunk: true
					}
				}
			});
			config.optimization.runtimeChunk('single');
		}
		// 增加进度条
		if (process.env.VUE_APP_BUILD_CONFIG?.includes('process')) {
			config.plugin('GhostProgressWebpackPlugin').use(new GhostProgressWebpackPlugin());
		}
		// 开启gzip打包(但是后端必须在nginx部署配置gzip的支持)
		if (process.env.VUE_APP_BUILD_CONFIG?.includes('gzip')) {
			config.plugin('compressionPlugin').use(
				new CompressionPlugin({
					filename: '[path].gz[query]',
					algorithm: 'gzip',
					test: productionGzipExtensions,
					threshold: 10240,
					minRatio: 0.8,
					deleteOriginalAssets: true
				})
			);
		}
		/**
		// 替换原有的 HappyPack 配置，用于多进程打包（和aieditor的babel-loader发生冲突，暂时屏蔽）
		if (process.env.VUE_APP_BUILD_CONFIG?.includes('multiCpu')) {
			config.module
				.rule('js')
				.test(/\.js$/)
				.exclude.add(/node_modules/)
				.end()
				.use('thread-loader')
				.loader('thread-loader')
				.options({
					// webpack 4 兼容的配置方式
					workers: cpuLength - 1,
					workerParallelJobs: 50
					// 避免使用新版本特有的选项
				})
				.before('babel-loader');
		}
		 */
		// 生成依赖分析
		if (process.env.VUE_APP_BUILD_CONFIG?.includes('dependencies')) {
			config.plugin('BundleAnalyzerPlugin').use(
				new BundleAnalyzerPlugin({
					analyzerMode: 'static', // 生成静态HTML文件
					reportFilename: 'report.html', // 报告文件名
					openAnalyzer: true // 不自动打开浏览器
				})
			);
		}
		// 添加 aieditor 的 Babel 转译规则（放在最前面）
		config.module
			.rule('aieditor')
			.test(/\.js$/)
			.include.add(/node_modules\/aieditor/)
			.end()
			.use('babel-loader')
			.loader('babel-loader')
			.end();
		const oneOfsMap = config.module.rule('scss').oneOfs.store;
		oneOfsMap.forEach(item => {
			item.use('sass-resources-loader').loader('sass-resources-loader').options({
				// 全局变量文件路径，只有一个时可将数组省去
				resources: './src/styles/index-mixin.scss'
			});
		});
	}
};
