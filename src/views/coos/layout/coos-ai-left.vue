<template>
	<div class="left-box">
		<div
			class="title"
			:style="
				$route.query.goBack == 1
					? 'padding-left:5px;'
					: robotLogo
					? `background-image: url(${robotLogo});   background-size: cover;
    background-repeat: no-repeat;`
					: ''
			"
		>
			<div class="title-left">
				<svg-icon
					v-if="$route.query.goBack == 1"
					class="title-icon"
					icon-class="seal-left"
					@click="goBack"
				></svg-icon>
				<span v-if="!robotLogo">
					{{ coosTitle }}
				</span>
			</div>
			<!--			<el-tooltip-->
			<!--				v-if="rentInfo.id === 1113"-->
			<!--				class="item"-->
			<!--				effect="dark"-->
			<!--				:content="`切换为${currentVersion === '1' ? '企业' : '政府'}版`"-->
			<!--				placement="right"-->
			<!--			>-->
			<!--  原本的切换版本事件     changeVersion>-->
			<div v-if="clientSystemMode == 5" class="title-right" @click="setInfo">
				<!--					<span class="title-text">{{ currentVersion === '1' ? '政府' : '企业' }}版</span>-->
				<img class="jxj-user" src="../../../assets/images/coos/jxj-user.png" alt="" />
				<!--				<i class="coos-iconfont icon-liebiaoxingshi title-icon"></i>-->
			</div>
			<!--			</el-tooltip>-->
		</div>
		<div class="left-scrollbar">
			<div class="message-type">
				<div v-show="isAiMode" class="add-massage" @click="createNewChat">
					<i class="coos-iconfont icon-chat_add"></i>
					创建新对话
				</div>
				<div class="type-list">
					<div
						v-for="(item, index) of newAiEnterList"
						v-show="!item.version || item.version === currentVersion"
						:key="'always-' + index"
						:class="typeActive === item.permsCode ? 'type-list-box type-active' : 'type-list-box'"
						@click="clickSome(item)"
					>
						<img class="ai-icon" :src="item.logoUrl" alt="" />
						<div class="custom-tip">{{ item.name }}</div>
					</div>
				</div>
			</div>
			<div v-show="isAiMode" class="message-records">
				<div class="message-records-title" @click="islist = !islist">
					<div>
						<i class="coos-iconfont icon-chat message-icon"></i>
						<span>最近对话</span>
					</div>
					<div class="bottom-icon-box">
						<i
							class="coos-iconfont icon-nav-bottom"
							:class="{ 'bottom-icon': true, 'bottom-act-icon': islist }"
						></i>
					</div>
				</div>
				<div
					v-infinite-scroll="scrollBottom"
					class="message-records-list"
					:infinite-scroll-disabled="disabled"
					:class="islist ? 'history-content' : ' show-list'"
				>
					<el-checkbox-group
						ref="historyListContainer"
						v-model="checkChat"
						v-loading="delLoading"
						class="message-records-list-item"
					>
						<div
							v-for="(item, index) of historyList"
							:key="index"
							:class="{ select: currentHistory === item.id || editInput === item.id }"
							class="history-content-item"
							@click="editInput !== item.id && !handleBatch && selectHistory(item.id, item.type)"
						>
							<el-checkbox v-if="handleBatch" :label="item.id" class="block">
								<span class="text">{{ item.title }}</span>
							</el-checkbox>
							<div v-else class="block">
								<el-input
									v-if="editInput === item.id"
									v-model="updateValue"
									v-loading="editLoading"
									@click.stop.native
									@keydown.enter.native="updateTittle(item.id, index)"
								></el-input>
								<span v-else class="text">{{ item.title }}</span>
								<div v-if="editInput === item.id" class="handle show-handle">
									<i
										class="coos-iconfont icon-selected handle-icon"
										@click.stop="updateTittle(item.id, index)"
									></i>
									<i
										class="coos-iconfont icon-close_circle handle-icon"
										@click.stop="editInput = ''"
									></i>
								</div>
								<div v-else-if="!item.isOtherData" class="handle">
									<i class="coos-iconfont icon-edit handle-icon" @click.stop="edit(item)"></i>
									<i
										class="coos-iconfont icon-trash handle-icon"
										@click.stop="delChatList(item.id)"
									></i>
								</div>
							</div>
						</div>
						<!--				<div v-show="delLoading" class="loading-text">加载中...</div>-->
						<div v-show="!delLoading && historyList.length === 0" class="empty">
							<img class="empty-image" :src="emptyImg" alt="" />
						</div>
					</el-checkbox-group>
				</div>
			</div>
		</div>
		<!--  点击头像的弹窗  -->
		<setting
			ref="setting"
			:more-style="{
				top: '70px',
				left: '70px'
			}"
		></setting>
	</div>
</template>
<script>
import { mapGetters } from 'vuex';
import { delChatList, getChatList, updateTittle } from '@/api/modules/coos';
import { assetsUrl, serveUrl } from '@/config';
import setting from '@/layout/components/setting/index.vue';
import { preUrl } from '@/config';
export default {
	name: 'CoosAiLeft',
	components: { setting },
	data() {
		return {
			isAiMode: true, // 是否为AI模型
			currentVersion: '1', // 政府企业版
			typeActive: '',
			historyList: [],
			emptyImg: assetsUrl + '/desk-app/empty.png',
			editLoading: false,
			updateValue: '', // 更新对话标题的信息
			editInput: '', // 编辑的数据id
			currentHistory: '', // 当前的历史索引
			currentMode: {}, // 当前模式
			sessionTypes: '',
			allSelect: false, // 全选
			// 右侧对话记录列表的分页参数
			chatListParam: {
				pageSize: 40,
				pageNo: 1,
				title: '' //搜索字段
			},
			listTotal: 0, //总条数
			islist: true,
			handleBatch: false, // 批量操作
			delLoading: false, // 删除的异步状态
			checkChat: [] // 被选中的对话记录
		};
	},
	computed: {
		...mapGetters(['userInfo', 'coosConfig', 'aiEnterList', 'rentInfo', 'clientSystemMode']),
		robotLogo() {
			return this.coosConfig.robotLogo ? serveUrl + this.coosConfig.robotLogo : '';
		},
		// 标题
		coosTitle() {
			return this.coosConfig.robotName || 'COOS智能助手';
		},
		newAiEnterList() {
			let arr = this.aiEnterList;
			try {
				arr = arr
					.filter(item => {
						let extend = item.extend ? JSON.parse(item.extend) : {};
						// 是不是AI模型快捷入口的菜单，过滤掉知识库、文档等指定模型
						return extend.quickEntrance === 'true';
					})
					.map(item => {
						let extend = item.extend ? JSON.parse(item.extend) : {};
						return {
							...item,
							type: item.permsCode.split('-')[1], // 取AI-后面的作为类型编码，唯一的
							modeType: extend.modeType, // 这不是唯一的
							// isFixEnter: extend.isFixEnter, // 接口根本没有这个字段，zuowenbo取
							sessionTypes: extend.chatType || '',
							webviewUrl: extend.PC || item.webviewUrl || '',
							version: '1'
						};
					});
			} catch (e) {
				console.log(e);
			}
			const modeType = this.$route.query.modeType;
			if (!this.currentMode.type) {
				this.trySetModeByType(modeType, arr);
			}
			return arr;
		},
		// 没有更多
		noMore() {
			if (this.historyList.length === 0) {
				return true;
			}
			return this.historyList.length >= this.listTotal;
		},
		// 是否可以继续出发滚动触底
		disabled() {
			return this.noMore;
		}
	},
	watch: {
		'$route.query.modeType': {
			handler(newVal) {
				if (!newVal) return;
				const modeType = newVal;
				if (this.newAiEnterList && this.newAiEnterList.length) {
					this.trySetModeByType(modeType, this.newAiEnterList);
					this.$nextTick(() => {
						window.location.replace(window.location.href.replace(/[&|?]?modeType=[^&]+/, ''));
					});
				}
			},
			immediate: true
		}
	},
	created() {
		// this.$nextTick(() => {
		// 	const modeType = this.$route.query.modeType;
		// 	this.trySetModeByType(modeType);
		// });
	},
	methods: {
		trySetModeByType(modeType, arr) {
			if (!Array.isArray(arr) || arr.length === 0) return;
			if (modeType) {
				const obj = arr.find(item => item.type === modeType);
				if (obj) {
					this.setMode(obj);
					return;
				}
			}
			// 如果没有匹配 type 或 modeType 不存在，则找 currentVersion 匹配项
			for (const item of arr) {
				if (item.version === this.currentVersion) {
					this.setMode(item);
					return;
				}
			}
			// 默认选第一个
			this.setMode(arr[0]);
		},
		/**切换政府企业版*/
		changeVersion() {
			this.currentVersion = this.currentVersion === '1' ? '2' : '1';
			let mode = this.newAiEnterList.filter(item => item.version === this.currentVersion)[0];
			this.setMode(mode);
			this.$emit('changeRole', this.currentVersion);
		},
		//  打开弹窗
		setInfo() {
			this.$refs.setting.open();
		},
		setMode(modeObj) {
			this.typeActive = modeObj.permsCode;
			this.currentMode = modeObj;
			this.$emit('setCurrentMode', this.currentMode);
			// 是AI模型才请求
			let extend = modeObj.extend ? JSON.parse(modeObj.extend) : {};
			this.isAiMode = extend.isAiMode === 'true';
			if (this.isAiMode) {
				this.sessionTypes = modeObj.sessionTypes;
				this.historyList = [];
				this.chatListParam.pageNo = 1;
				this.getChatRecords();
			}
		},
		/**返回*/
		goBack() {
			this.$router.go(-1);
		},
		clickSome(item) {
			if (this.typeActive === item.permsCode) {
				return;
			}
			let extend = item.extend ? JSON.parse(item.extend) : {};
			let url = extend.PC || '';
			// 拼接网页标题和AI编码，保持网页标题和菜单名称保持一致，新页面根据AI编码获取配置
			url =
				url.indexOf('?') > -1
					? url + `&title=${item.name}&permsCode=${item.permsCode}`
					: url + `?title=${item.name}&permsCode=${item.permsCode}`;
			let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
			// 外部系统
			if (extend.isExternal) {
				if (extend.openType === 2) {
					window.open(mainUrl);
				} else {
					this.$router.push({
						path: `/other-system?url=${encodeURIComponent(mainUrl)}`
					});
				}
			}
			// 内部智能对话
			else if (extend.modeType) {
				this.setMode.call(this, item);
			}
			// 新窗口打开
			else if (extend.openType === 2) {
				window.open(mainUrl);
			}
			// 本窗口跳转路由
			else {
				this.$router.push(extend.PC);
			}
		},
		createNewChat() {
			this.$emit('createNewChat');
		},
		/**右边对话记录滚动加载*/
		scrollBottom() {
			this.chatListParam.pageNo += 1;
			this.getChatRecords();
		},
		/**点击历史记录*/
		selectHistory(id, type) {
			// if (
			// 	this.currentMode.permsCode === getDictionary('AI编码/创意智绘') &&
			// 	this.rentInfo.id === 1113
			// ) {
			// 	window.open(
			//
			// 	);
			// } else if (this.sessionId !== id) {
			this.currentHistory = id;
			this.$emit('changeSessionId', id, type);
			// }
		},
		/**更新对话标题*/
		updateTittle(id, index) {
			this.editLoading = true;
			updateTittle(id, { title: this.updateValue }).then(res => {
				this.editLoading = false;
				if (res.code === 200) {
					this.editInput = '';
					this.historyList[index].title = this.updateValue;
					this.$message.success('更新成功！');
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**点击编辑*/
		edit(item) {
			this.editInput = item.id;
			this.updateValue = item.title;
		},
		/**重置历史记录*/
		reset() {
			this.currentHistory = '';
		},
		/**重新回去历史记录*/
		resetList() {
			this.historyList = [];
			this.chatListParam.pageNo = 1;
			this.getChatRecords();
		},
		/**删除右侧对话记录*/
		delChatList(ids) {
			this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.delLoading = true;
					delChatList(ids).then(res => {
						if (res.code === 200) {
							this.allSelect = false;
							this.historyList = this.historyList.filter(item => item.id !== ids);
							this.delLoading = false;
							this.$message.success('操作成功');
						} else {
							this.delLoading = false;
							this.$message.error(res.message);
						}
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		/**获取对话记录*/
		async getChatRecords() {
			this.delLoading = true;
			let res;
			let historyList = [];
			let listTotal = 0;
			try {
				// if (
				// 	this.currentMode.permsCode === getDictionary('AI编码/创意智绘') &&
				// 	this.rentInfo.id === 1113
				// ) {
				// 	res = await getWriteHistory({
				// 		handlerType: '',
				// 		pageNum: this.chatListParam.pageNo,
				// 		pageSize: this.chatListParam.pageSize
				// 	});
				// 	if (res.rCode !== 0) {
				// 		throw new Error(res.msg);
				// 	}
				// 	historyList = res.results.records.map(item => {
				// 		return { ...item, title: item.docTitle, isOtherData: true };
				// 	});
				// 	listTotal = res.results.totalCount;
				// } else {
				let params = { ...this.chatListParam, sessionTypes: this.sessionTypes };
				if (this.$route.query.viewAllHistory && this.$route.query.viewAllHistory === 'true') {
					params.viewAllHistory = true;
				}
				res = await getChatList(params);
				if (res.code !== 200) {
					throw new Error(res.message);
				}
				historyList = res.result.records;
				listTotal = res.result.total;
				// }
				this.delLoading = false;
				if (this.chatListParam.pageNo === 1) {
					this.historyList = historyList;
				} else {
					this.historyList = this.historyList.concat(historyList);
				}
				this.listTotal = listTotal;
			} catch (err) {
				this.delLoading = false;
				console.log(err);
			}
		}
	}
};
</script>
<style scoped lang="scss">
.title {
	font-family: PingFang SC, PingFang SC;
	font-weight: 600;
	font-size: 20px;
	color: #15224c;
	line-height: 23px;
	width: 280px;
	min-height: 64px;
	border-radius: 0px 0px 0px 0px;
	border-bottom: 1px solid #dae8f4;
	padding: 10px 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.title-left {
	@include flexBox();
}
.title-right {
	color: var(--brand-6);
	cursor: pointer;
	@include flexBox();
}
.title-text {
	font-size: 16px;
}
.title-icon {
	font-size: 16px;
	margin-left: 4px;
}
.message-type {
	padding: 12px 6px;
	.add-massage {
		padding: 13px 8px;
		border-radius: 6px 6px 6px 6px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		background: var(--brand-1);
		border: 1px solid var(--brand-3) !important;
		color: var(--brand-6) !important;
		line-height: 22px;
		text-align: left;
		font-style: normal;
		text-transform: none;
		cursor: pointer;
		margin-bottom: 12px;
	}
	.type-list {
		.type-active {
			background: #fff;
		}
		&-box {
			border-radius: 6px 6px 6px 6px;
			display: flex;
			align-items: center;
			margin-bottom: 4px;
			padding: 12px 8px;
			cursor: pointer;
			.ai-icon {
				width: 26px;
				height: 26px;
			}
		}
	}
}
.message-records {
	display: flex;
	flex-direction: column;
	flex: 1;
	margin-top: 12px;
	overflow: hidden;
	border-radius: 0px 0px 0px 0px;
	border-top: 1px solid #dae8f4;
	&-title {
		padding: 12px 14px 12px 16px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #15224c;
		display: flex;
		align-items: center;
		justify-content: space-between;
		line-height: 22px;
		.message-icon {
			margin-right: 3px;
		}
	}
	.show-list {
		width: 100%;
		height: 0;
		overflow: hidden;
		padding: 0 9px;
		transition: height 0.5s ease;
	}
	.history-content {
		width: 100%;
		flex: 1;
		overflow: auto;
		transition: height 0.5s ease;
		padding: 0 9px;

		.loading-text {
			height: 40px;
			line-height: 40px;
			text-align: center;
			color: #c0c0c0;
		}

		.empty {
			height: 100%;
			width: 100%;
			max-width: 217px;
			@include flexBox();

			&-image {
				height: 142px;
			}
		}

		@include noScrollBar;

		&-item {
			cursor: pointer;
			padding: 0 72px 0 17px;
			height: 38px;
			border-radius: 6px;
			position: relative;
			@include flexBox(flex-start);

			.block {
				overflow: hidden;
				@include flexBox(flex-start);

				::v-deep .el-checkbox__label {
					overflow: hidden;
					@include flexBox(flex-start);
				}
			}

			&-check {
				width: 16px;
				height: 16px;
				border-radius: 2px;
				border: 1px solid #dcdfe6;
				margin-right: 9px;
				cursor: pointer;
			}

			::v-deep .el-input__inner {
				border: none;

				&:hover {
					border: none;
				}

				&:focus {
					border: none;
					box-shadow: none;
				}
			}

			&:hover {
				background: #fff;

				.handle {
					display: block;
				}
			}

			& .icon {
				flex-shrink: 0;
				font-size: 20px;
				margin-right: 8px;
				color: $primaryTextColor;
			}

			& .text {
				flex: 1;
				font-size: 13px;
				font-weight: 400;
				color: #2f446b;
				line-height: 22px;
				@include aLineEllipse;
			}

			.handle {
				display: none;
				position: absolute;
				right: 16px;

				&-icon {
					font-size: 18px;
					margin-left: 10px;
					color: $subTextColor;
				}
			}

			.show-handle {
				display: block;
			}
		}

		.select {
			background: #f5f7fa;
		}
	}
}
.left-box {
	height: 100%;
}
.left-scrollbar {
	height: calc(100% - 80px);
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	@include noScrollBar();
}

.bottom-icon {
	cursor: pointer;
	position: absolute;
	bottom: -5px;
	right: 5px;
	transform: rotate(0deg); /* 初始状态向下 */
	transition: transform 0.3s ease;
}

.bottom-act-icon {
	transform: rotate(-180deg); /* 展开状态向上 */
}
.bottom-icon-box {
	position: relative;
}
.empty-image {
	width: 100%;
	height: 100%;
}
.message-records-list-item {
	overflow-y: auto;

	width: 100%;
	//height: 100%;
}
.custom-tip {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400 !important;
	font-size: 14px !important;
	line-height: 20px;
}
.robotLogo {
	width: 280px;
}
.jxj-user {
	width: 16px;
}
</style>
