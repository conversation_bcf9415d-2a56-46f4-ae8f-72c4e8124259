<template>
	<div class="query-item" :class="{ znwsQueryItem: znwsStyle }">
		<div
			v-if="item.createTime && !hideTime"
			class="query-item-time"
			:style="{ marginTop: index === 0 ? '12px' : '28px' }"
		>
			{{ item.createTime }}
		</div>
		<!--  消息展示  -->
		<div class="query-item-message isSelf">
			<FileById
				v-if="coosAvatar"
				:more-style="{
					borderRadius: '6px',
					margin: '0 0 0 10px'
				}"
				:size="40"
				:value="userInfo.avatarUrl"
				:default-font-icon="userInfo.realname.slice(-1) || '用户'"
			></FileById>
			<!--   根据新旧页面不同采用不同的渲染   -->
			<div
				v-if="coosVersion === 'coosAi'"
				class="detail"
				:style="{
					background: isComponent
						? '#EDF2F7'
						: uglyColor
						? uglyColor
						: znwsStyle
						? 'var(--brand-6)'
						: '#EDF2F7'
				}"
			>
				<!--  语音以及文本消息  -->
				<pre class="detail-pre">{{ item.query }}</pre>
			</div>
			<div
				v-else
				class="detail"
				:style="{
					background: isComponent
						? '#F4F7FB'
						: uglyColor
						? uglyColor
						: znwsStyle
						? 'var(--brand-6)'
						: '#ffffff'
				}"
			>
				<!--  语音以及文本消息  -->
				<pre class="detail-pre">{{ item.query }}</pre>
			</div>
		</div>
		<!--          操作-->
		<div class="operation">
			<div class="operation-box" @click="handleCopy(item.query)">
				<img src="../../../assets/images/coos/copy.png" alt="" class="operation-box-img" />
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
	name: 'CoosQuery',
	props: {
		hideTime: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// coos版本
		coosVersion: {
			type: String,
			default: () => {
				return 'coos';
			}
		},
		// 待办的时候是组件模式
		isComponent: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		znwsStyle: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		//  是否展示头像
		coosAvatar: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		index: {
			type: Number,
			default: () => {
				return 0;
			}
		},
		uglyColor: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	methods: {
		fallbackCopyText(text) {
			const textarea = document.createElement('textarea');
			textarea.value = text;
			document.body.appendChild(textarea);
			textarea.select();
			try {
				document.execCommand('copy');
				this.$message.success('复制成功');
			} catch (err) {
				this.$message.error('复制失败，请手动选择复制');
			} finally {
				document.body.removeChild(textarea);
			}
		},
		handleCopy(text) {
			if (navigator.clipboard) {
				navigator.clipboard
					.writeText(text)
					.then(() => {
						this.$message.success('复制成功！');
					})
					.catch(err => {
						console.error('复制失败: ', err);
						this.fallbackCopyText(text);
					});
			} else {
				this.fallbackCopyText(text);
			}
		}
	}
};
</script>

<style scoped lang="scss">
.query-item {
	&-time {
		font-size: 12px;
		font-weight: 400;
		color: #515b6e;
		line-height: 20px;
		text-align: center;
		margin-bottom: 24px;
	}
	.isSelf {
		flex-direction: row-reverse;
		.detail {
			border-radius: 6px 0px 6px 6px;
		}
	}
	&-message {
		@include flexBox(flex-start, flex-start);
		margin-bottom: 14px;
		.detail {
			padding: 12px;
			max-width: 50%;
			border-radius: 0px 6px 6px 6px;
			font-size: 14px;
			font-weight: 400;
			color: #000000;
			line-height: 22px;
			position: relative;
			&-pre {
				margin: 0;
				white-space: pre-wrap; //pre-line
				word-wrap: break-word;
				user-select: text;
			}
		}
	}
}
.znwsQueryItem {
	.detail {
		border-radius: 12px !important;
		color: #fff !important;
	}
}
.operation {
	margin: 8px 0;
	display: flex;
	flex-direction: row-reverse;
	&-box {
		width: 24px;
		height: 24px;
		background: #ffffff;
		box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.06);
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		&-img {
			width: 18px;
		}
	}
}
</style>
