<template>
	<div id="step-content" class="step step-pc">
		<div v-for="(item, index) of stepList" :key="index" class="step-item">
			<div class="step-item-top">
				<div
					v-if="currentStep > index"
					class="active-img"
					:style="{ background: uglyColor ? uglyColor : 'var(--brand-6)' }"
				>
					<img class="step-item-icon" src="../../../assets/images/coos/new-finish.png" />
				</div>
				<img v-else class="step-item-icon step-img" src="@/assets/images/coos/wait.png" />
				<div
					v-if="index < stepList.length - 1"
					class="line-content"
					:style="{ width: (stepWidth - 10 * 4) / 4 + 'px' }"
				>
					<span
						v-if="currentStep > index + 1"
						class="line finish-line"
						:style="{ backgroundColor: uglyColor ? uglyColor : 'var(--brand-6)' }"
					></span>
					<!--					<img-->
					<!--					-->
					<!--						src="@/assets/them-coos/coos//line.png"-->
					<!--					/>-->
					<img class="line" src="@/assets/images/coos/wait-line.png" />
				</div>
			</div>
			<span class="step-item-text">{{ item.name }}</span>
		</div>
	</div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
	name: 'MessageStep',
	props: {
		currentStep: {
			type: Number,
			default: () => {
				return 2;
			}
		},
		uglyColor: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			stepList: [
				{
					name: '问题分析',
					type: 'documentAnalysis'
				},
				{
					name: '问题搜索',
					type: 'documentSearch'
				},
				{
					name: '整理答案',
					type: 'tidyanswer'
				},
				{
					name: '完成',
					type: 'finish'
				}
			],
			stepWidth: 0
		};
	},
	computed: {
		...mapGetters(['isPc'])
	},
	mounted() {
		const el = this.$el.querySelector('#step-content');
		if (el) {
			this.stepWidth = el.offsetWidth;
		}
	}
};
</script>
<style scoped lang="scss">
.step {
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	height: 70px;
	border-radius: 12px;
	margin: 0 0 12px;
}

.step-app {
	width: 345px; /* 690rpx */
	position: relative;
	z-index: -1;
}

.step-pc {
	width: 600px;
	display: flex;
	align-items: center;
}
.step-item {
	min-width: 48px;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.step-item-icon {
	//width: 20px;
	//height: 20px;
	//border-radius: 50%;
	//margin-bottom: 8px;
	z-index: 2;
}
.step-img {
	width: 20px;
	height: 20px;
	border-radius: 50%;
	margin-bottom: 8px;
	z-index: 2;
}

.step-item-top {
	position: relative;
}
@keyframes changeLine {
	from {
		width: 0;
	}
	to {
		width: 100%;
	}
}

.finish-line {
	z-index: 1;
	animation: changeLine 1s forwards;
}

.line-content {
	position: absolute;
	top: 9.5px; /* 19rpx */
	left: 20px; /* 40rpx */
}
.finish-line,
.line {
	width: 138px !important; // 统一使用百分比或固定 px
}
.line {
	width: 138px;
	height: 2px;
	position: absolute;
	top: 0;
	left: 13px;
}

.active {
	background: #187bf3;
}

.step-item-text {
	font-family: PingFangSC, PingFang SC;
	font-weight: 500;
	font-size: 12px;
	color: #2f446b;
	line-height: 16.5px;
}
.active-img {
	width: 20px;
	height: 20px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
}
</style>
