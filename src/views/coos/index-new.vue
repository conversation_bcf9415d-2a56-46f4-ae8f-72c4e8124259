<template>
	<div v-loading="loading" class="im-content" @click="closeKnowledgeBase">
		<div class="left">
			<coos-ai-left
				v-show="!standaloneDisplay"
				ref="CoosAiLeft"
				@setCurrentMode="setCurrentMode"
				@createNewChat="createNewChat"
				@changeSessionId="changeSessionId"
				@changeRole="changeRole"
			></coos-ai-left>
		</div>
		<div v-if="currentMode.permsCode === getDictionary('AI编码/ppt')" class="right">
			<PptCoos
				ref="pptCoos"
				:current-mode="currentMode"
				:chat-type="currentMode.sessionTypes"
				@getChatRecords="getChatRecords"
			></PptCoos>
		</div>
		<div v-else-if="currentMode.permsCode === getDictionary('AI编码/智能问数')" class="right">
			<analysis
				ref="analysis"
				:current-mode="currentMode"
				:chat-type="currentMode.sessionTypes"
				:is-visible="false"
				@getChatRecords="getChatRecords"
			/>
		</div>
		<div v-else-if="currentMode.permsCode === getDictionary('AI编码/写作助手3')" class="right">
			<draftWriting></draftWriting>
		</div>
		<div v-else-if="currentMode.permsCode === getDictionary('AI编码/问答助手')" class="right">
			<coosZnws
				:key="coosZnwsKey"
				ref="coosZnws"
				:extend="extend"
				:ugly="ugly"
				:ugly-color="uglyColor"
				:current-mode-buttons="currentModeButtons"
				:current-mode="currentMode"
				@boxScrollLoad="boxScrollLoad"
				@getChatRecords="getChatRecords"
			/>
		</div>
		<div
			v-else-if="
				[getDictionary('AI编码/智能问数H5'), getDictionary('AI编码/智能问政H5')].includes(
					currentMode.permsCode
				)
			"
			class="right"
		>
			<coosH5 ref="H5" :key="currentMode.permsCode" :webview-url="extend.PC"></coosH5>
		</div>
		<!--经信局演示定制开发(意见建议)-->
		<!--		<div v-else-if="currentMode.type === 'yjjy'" class="right">-->
		<!--			<coosSuggestions ref="coosSuggestions"></coosSuggestions>-->
		<!--		</div>-->
		<!--经信局演示定制开发(知识库)-->
		<div
			v-else-if="currentMode.permsCode === getDictionary('AI编码/知识库')"
			class="right document-right"
		>
			<coos-document-menu
				v-if="extend.hideKnowledgeMenu !== 'true'"
				class="document-menu"
				:extend="extend"
				@searchDialog="searchDialog"
				@selectChange="selectChange"
			></coos-document-menu>
			<documentContent
				ref="documentContent"
				:is-components="true"
				:show-label="extend.hideKnowledgeMenu === 'true'"
				:component-hide-menu="true"
				style="flex: 1; overflow: hidden"
			></documentContent>
			<coosSearch
				:dialog-visible.sync="dialogVisible"
				@handleSearchFile="handleSearchFile"
			></coosSearch>
		</div>
		<div v-else-if="currentMode.permsCode" class="right" :style="`background: ${rightBackground}`">
			<div
				v-show="extend.forwardLookVersion === 'open' && !prospectiveVersionType"
				class="to-new"
				@click="toNew"
			>
				<i class="coos-iconfont icon-lianjie to-new-icon"></i>
				<span>{{ isProspectiveVersion ? '基础版' : '前瞻版' }}</span>
			</div>
			<div v-if="isProspectiveVersion" class="component-content">
				<recordingTranscript
					v-if="prospectiveVersionType === 'recordingTranscript'"
					go-back-name="返回"
					:custom-go-back="customGoBack"
				></recordingTranscript>
				<webviewBox
					v-else-if="prospectiveVersionType === 'webviewBox'"
					:view-url="prospectiveVersionUrl"
					:need-socket="false"
				></webviewBox>
				<cover
					v-else
					:forward-look-sections="extend.forwardLookSections"
					@changeType="changeType"
				></cover>
			</div>
			<div v-else class="right-box">
				<div class="right-box-item">
					<div
						v-if="!messageList.length"
						class="right-box-header"
						:style="{ marginTop: messageList.length ? '0' : '120px' }"
					>
						<div v-if="extend && extend.bgUrl" class="right-header">
							<img class="right-header-img" :src="extend.bgUrl" alt="" />
						</div>
						<div class="right-title">{{ extend.title || currentMode.name }}</div>
						<div class="right-dec">{{ currentMode.digest }}</div>
					</div>
					<div
						v-else
						id="coos-detail-con"
						ref="messageContent"
						class="message-list"
						@scroll="scroll"
					>
						<div
							v-for="(item, index) of messageList"
							:key="index"
							style="width: 100%"
							:style="{ marginTop: index === 0 ? FirstAnswerMargin : '' }"
							@contextmenu="contextmenu($event, item, index)"
						>
							<!--  提问的展示、选择知识库的展示  -->
							<CoosQuery
								v-if="item.type === 'query'"
								:ref="'message-item-' + index"
								coos-version="coosAi"
								:is-component="isComponent"
								:item="item"
								:index="index"
							></CoosQuery>
							<!--  选择知识库后的回答  -->
							<CoosAnswer
								v-else-if="item.type === 'answer'"
								:ref="'message-item-' + index"
								:is-component="isComponent"
								:item="item"
								:is-pause="index === messageList.length - 1 ? isPause : false"
								:index="index"
								:session-id="sessionId"
								@refresh="refresh"
								@addMoreApplicationAnswer="addMoreApplicationAnswer"
								@continueQuery="continueQuery"
								@addMessage="addMessage"
								@handleMoreRecommend="send"
							></CoosAnswer>
						</div>
					</div>
					<div>
						<div v-if="fileQuery" class="file-tip">
							<div class="file-tip-left">
								<svg-icon class="file-tip-image" :icon-class="getBackground(fileQuery)"></svg-icon>
								<span class="file-tip-text">正在基于文件“</span>
								<span class="file-tip-name">{{ fileQuery.name }}</span>
								<span class="file-tip-text">”为你作答</span>
							</div>
							<span class="file-tip-btn" @click="clearFile">清空</span>
						</div>
					</div>
					<div v-if="isPause" class="pause">
						你停止生成了本次回答
						<span class="pause-text" @click="sendInputMessage">重新编辑问题</span>
					</div>
					<!--  输入内容  -->
					<div class="message-send">
						<div class="message-send-box" :style="{ height: height + 'px' }">
							<el-input
								v-model="sendCon"
								class="message-send-input"
								resize="none"
								type="textarea"
								autosize
								:placeholder="currentMode.prologue || '请输入问题'"
								@keydown.native="preventEnter"
								@keyup.native="sendMessage"
							></el-input>
						</div>
						<!--          工具栏-->
						<div class="message-utils">
							<!--         以文写文     -->
							<div
								v-if="currentModeButtons.includes('ywxw')"
								class="message-utils-select ywxw"
								@click="handleButton('ywxw')"
							>
								<i class="coos-iconfont icon--logo icon deepThink"></i>
								<span>{{ extend.buttonsConfig.ywxw.name }}</span>
							</div>
							<div
								v-if="currentModeButtons.includes('defaultKnowledge')"
								:class="
									searchType === 'defaultKnowledge'
										? 'message-utils-select IsInternetSearch IsInternetSearch-box'
										: 'message-utils-select'
								"
								@click.stop
							>
								<i class="coos-iconfont icon-zhuanxie icon"></i>
								<span
									:class="searchType === 'defaultKnowledge' ? 'text IsInternetSearch' : 'text'"
									@click="internetSearch('defaultKnowledge')"
								>
									{{ extend.buttonsConfig.defaultKnowledge.name }}
								</span>
							</div>
							<!--            深度思考-->
							<el-tooltip
								v-if="currentModeButtons.includes('deepThink')"
								class="item"
								effect="dark"
								content="开启推理模式，进行深度思考"
								placement="top"
							>
								<!--								v-if="['AI-cyzh', 'AI-zdbk'].includes(currentMode.permsCode)"-->
								<div
									:class="
										deepThink
											? 'message-utils-select IsInternetSearch IsInternetSearch-box'
											: 'message-utils-select'
									"
									@click.stop
								>
									<i class="coos-iconfont icon-shenduss icon deepThink"></i>
									<span
										:class="deepThink ? 'text IsInternetSearch' : 'text'"
										@click="internetSearch('deepThink')"
									>
										深度思考
									</span>
								</div>
							</el-tooltip>

							<el-dropdown
								v-if="currentModeButtons.includes('thinkMode')"
								class="message-utils-select IsInternetSearch IsInternetSearch-box"
								trigger="click"
							>
								<div class="dropdown-link">
									<span>{{ currentThinkMode === 'base' ? '常规模式' : '专业模式' }}</span>
									<i class="el-icon-arrow-down el-icon--right"></i>
								</div>
								<el-dropdown-menu slot="dropdown">
									<el-dropdown-item @click.native.stop="internetSearch('currentThinkMode', 'base')">
										<div
											class="think-mode"
											:class="{ 'current-think-mode': currentThinkMode === 'base' }"
										>
											常规模式
										</div>
									</el-dropdown-item>
									<el-dropdown-item
										@click.native.stop="internetSearch('currentThinkMode', 'number')"
									>
										<div
											class="think-mode"
											:class="{ 'current-think-mode': currentThinkMode === 'number' }"
										>
											专业模式
										</div>
									</el-dropdown-item>
								</el-dropdown-menu>
							</el-dropdown>

							<!--            联网搜索-->
							<!--							currentMode.permsCode === 'AI-zdbk' &&-->
							<div
								v-if="currentModeButtons.includes('onlineSearch')"
								:class="
									searchType === 'IsInternetSearch'
										? 'message-utils-select IsInternetSearch IsInternetSearch-box'
										: 'message-utils-select'
								"
								@click.stop
							>
								<i class="coos-iconfont icon-wangluo icon"></i>
								<span
									:class="searchType === 'IsInternetSearch' ? 'text IsInternetSearch' : 'text'"
									@click="internetSearch('IsInternetSearch')"
								>
									联网搜索
								</span>
							</div>

							<div
								v-if="currentModeButtons.includes('knowledgeBase')"
								:class="
									searchType === 'isKnowledge'
										? 'message-utils-select IsInternetSearch IsInternetSearch-box'
										: 'message-utils-select'
								"
								@click.stop
							>
								<i class="coos-iconfont icon-caidanlan-bangong-zhishiku icon zhishiku"></i>
								<span
									:class="searchType === 'isKnowledge' ? 'text IsInternetSearch' : 'text'"
									@click="internetSearch('isKnowledge')"
								>
									知识库搜索
								</span>
							</div>
							<div
								v-if="currentModeButtons.includes('administrativeAssistant')"
								:class="
									searchType === 'administrativeAssistant'
										? 'message-utils-select IsInternetSearch IsInternetSearch-box'
										: 'message-utils-select'
								"
								@click.stop
							>
								<i class="coos-iconfont icon-caidanlan-bangong-zhishiku icon zhishiku"></i>
								<span
									:class="
										searchType === 'administrativeAssistant' ? 'text IsInternetSearch' : 'text'
									"
									@click="internetSearch('administrativeAssistant')"
								>
									办公助手
								</span>
							</div>

							<!--  知识库选择  -->
							<el-popover
								v-if="searchType === 'isKnowledge'"
								placement="top"
								trigger="click"
								popper-class="knowledge-base-parent"
								:visible-arrow="false"
							>
								<div v-show="knowledgeBase" class="knowledge-base" @click.stop>
									<div class="knowledge-base-title">
										<span>已选择：</span>
										<span class="data">
											<span class="data-select">{{ checkList.length }}</span>
											/{{ knowledge.length }}个知识库
										</span>
										<span class="button" @click="clearCheck">清空</span>
									</div>
									<el-checkbox-group v-model="checkList" class="knowledge-base-con">
										<el-checkbox
											v-for="item of knowledge"
											:key="item.id"
											:label="item.id"
											:disabled="checkList.length === 1 && checkList.includes(item.id)"
											class="knowledge-base-item"
										>
											<i class="coos-iconfont icon-cz-gzzd icon"></i>
											<span class="title">
												{{ item.spaceName }}
											</span>
										</el-checkbox>
									</el-checkbox-group>
								</div>
								<!--            知识库-->
								<div slot="reference" class="message-utils-select" @click.stop="showKnowledgeBase">
									<span class="text">
										{{
											checkList.length ? `将从${checkList.length}个知识库中为你回答` : '选择知识库'
										}}
									</span>
									<i class="coos-iconfont icon-nav-bottom handle-icon"></i>
								</div>
							</el-popover>

							<el-divider v-if="searchType === ''" direction="vertical"></el-divider>
							<!--							<i-->
							<!--								v-if="currentModeButtons.includes('imageUpload') && searchType === ''"-->
							<!--								class="coos-iconfont icon-tu more-icon"-->
							<!--								@click="pickFile('img')"-->
							<!--							></i>-->
							<div v-if="currentModeButtons.includes('imageUpload') && searchType === ''">
								<i
									class="coos-iconfont icon-folder folder-file-icon more-icon"
									@click="pickFile('file')"
								/>
								<input
									ref="fileInput"
									type="file"
									:accept="accept"
									style="display: none"
									@change="onPickFile($event)"
								/>
							</div>
							<div
								v-if="
									[getDictionary('AI编码/智控领航'), getDictionary('AI编码/数据智析')].includes(
										currentMode.permsCode
									)
								"
								class="message-utils-select"
							>
								<i class="coos-iconfont icon-chajiangongneng zklh-icon"></i>
								<div v-if="currentPlugin || currentDataDrilling" class="plugin-choose">
									<div @click="lookMore">
										<span class="text">选择了</span>
										<span class="text plugin-text">
											{{
												currentMode.permsCode === getDictionary('AI编码/智控领航')
													? currentPlugin.name
													: currentDataDrilling.moduleName
											}}
										</span>
										<span class="text">
											{{
												currentMode.permsCode === getDictionary('AI编码/智控领航')
													? '功能'
													: '业务数据'
											}}
										</span>
									</div>
									<span class="text plugin-reset" @click="handlePluginReset">重置</span>
								</div>
								<span v-else class="text" @click="lookMore">
									{{
										currentMode.permsCode === getDictionary('AI编码/智控领航')
											? '请选择功能'
											: '请选择业务数据'
									}}
								</span>
							</div>
							<div v-if="messageList.length">
								<i class="coos-iconfont icon-qingli-L-copy quick-button-icon" @click="clear"></i>
							</div>

							<!--           发送暂停  -->
							<div class="send-pause">
								<div v-if="IsSend" class="send-pause-pause-icon" @click="pause">
									<div class="pause-icon"></div>
								</div>
								<div
									v-else
									:class="
										sendCon.length
											? 'active-send-icon send-pause-send-icon'
											: 'send-pause-send-icon'
									"
								>
									<i class="coos-iconfont icon-fasong1" @click="send"></i>
								</div>
							</div>
						</div>
					</div>
					<!--     推荐   -->
					<coos-ai-recommend
						v-if="!isComponent && !loading && messageList.length === 0"
						:current-mode="currentMode"
						:is-component="isComponent"
						:questions-list="questionsList"
						:question-loading="questionLoading"
						:questions-num="questionsNum"
						:current-plugin="currentPlugin"
						:current-data-drilling="currentPlugin"
						:plugin-or-data-drilling="pluginOrDataDrilling"
						@otherQuestions="otherQuestions"
						@questionClick="send"
						@lookMore="lookMore"
						@clickSome="clickSome"
					></coos-ai-recommend>
				</div>
				<div class="bottom">本服务内容由人工智能整理生成，所提供的信息仅供参考。</div>
			</div>
		</div>
		<PluginPopup
			ref="PluginPopup"
			:init-plugin="currentPlugin"
			@selectPlugin="selectPlugin"
		></PluginPopup>
		<dataDrillingPopup
			ref="dataDrillingPopup"
			:init-data-drilling="currentDataDrilling"
			@selectDataDrilling="selectDataDrilling"
		></dataDrillingPopup>
		<flotAi v-if="individuallyAi"></flotAi>
	</div>
</template>

<script>
import { getDictionary } from '@/utils/data-dictionary';
import getBackground from '@/utils/get-file-icon';
import CoosAiLeft from '@/views/coos/layout/coos-ai-left.vue';
import { debounce, isEmpty } from '@/utils';
import {
	getChangePluginInit,
	getCoosHistory,
	getDataDrilling,
	getKnowledge,
	getQuestionsList,
	getUUID,
	somePlugins,
	uploadCoosFile
} from '@/api/modules/coos';
import CoosAiRecommend from '@/views/coos/layout/coos-ai-recommend.vue';
const { customUploadFileHandler } = uploadCoosFile();
import queryMixins from '@/views/coos/utils/query-mixins';
import CoosQuery from '@/views/coos/message/coos-query.vue';
import CoosAnswer from '@/views/coos/message/coos-answer.vue';
import PptCoos from '@/views/ppt/index.vue';
import PluginPopup from '@/views/coos/components/coos-plugin-popup.vue';
import dataDrillingPopup from '@/views/coos/components/coos-drilling-popup.vue';
import documentContent from '@/views/document-content/index.vue';
// import coosSuggestions from '@/views/coos/components/coos-suggestions.vue';
import coosH5 from '@/views/coos/components/coos-h5.vue';
import coosZnws from '@/views/coos/coos-model/coos-znws/index.vue';
import coosSearch from '@/views/coos/components/coos-search.vue';
import flotAi from '@/components/flot-ai/index.vue';
import coosDocumentMenu from '@/views/coos/components/coos-document-menu.vue';
import { mapGetters } from 'vuex';
import { preUrl } from '@/config';
import cover from '@/views/coos/components/cover.vue';
import recordingTranscript from '@/views/recording-transcript/index.vue';
import webviewBox from '@/components/webview-box/index.vue';
import { existsDgg } from '@/api/modules/coos-znwz';
import draftWriting from '@/views/draft-writing/index.vue';
export default {
	name: 'CoosAi',
	components: {
		flotAi,
		dataDrillingPopup,
		documentContent,
		coosSearch,
		// coosSuggestions,
		coosH5,
		coosDocumentMenu,
		cover,
		coosZnws,
		recordingTranscript,
		webviewBox,
		draftWriting,
		analysis: () => {
			if (process.env.VUE_APP_COOS === 'true') {
				return import('@/third-party/coos/views/analysis/index');
			} else {
				return 'div';
			}
		},
		PluginPopup,
		CoosAnswer,
		CoosQuery,
		CoosAiRecommend,
		CoosAiLeft,

		PptCoos
	},
	mixins: [queryMixins],
	props: {},
	data() {
		return {
			extend: {},
			prospectiveVersionType: '', // 前瞻版组件类型
			prospectiveVersionUrl: '', // 前瞻版组件的渲染地址
			isProspectiveVersion: false, // 是不是前瞻版
			nextType: '', // 下一步操作模式
			currentVersion: '1', // 演示：当前版本
			getModalData: {}, // 演示：获取模式的数据
			createBy: {
				1: '1764948864679231490',
				2: '1780842232101662721'
			}, // 演示：用户权限
			currentThinkMode: 'base', // 当前思考模式
			standaloneDisplay: false, // 是否独立显示
			ugly: false, // 丑陋版本
			uglyColor: '', // 丑陋的颜色
			individuallyAi: false,
			isComponent: false,
			menuLeft: 0,
			menuTop: 0,
			processStatus: '#19be6b', // 进度条颜色
			fileQuery: null, // 当前上传的文件
			accept: '*',
			menuShow: false,
			IsSend: false,
			params: {
				pageSize: 3,
				pageNo: 1
			},
			deepThink: false,
			loading: false, // 数据加载loading
			menuIndex: 0, // 右键菜单所在消息索引
			clickRightMessage: '', // 右键操作的菜单消息
			initHeight: 100, // 聊天框初始高度
			height: 100, // 变化的高度
			currentPlugin: null, // 当前选中功能
			currentDataDrilling: null, // 当前数据智析
			currentMode: {
				type: ''
			}, // 当前模式
			sessionId: '', // 当前对话的ID
			sendCon: '', // 发送内容
			inputMessage: '', // 缓存上一次发送的内容 用于重新编辑 重新生成
			messageList: [],
			isPause: false, //是否重新编辑
			messageId: 0,
			controller: null,
			searchType: '', // 联网搜索 知识库搜索
			contentEl: null, // 内容区域
			lock: false, //还有没有更多消息，请求锁
			lockAutoScroll: false, // 锁住自动滚动
			questionsList: [],
			currentModeButtons: [],
			questionParams: { pageNo: 1, pageSize: 4, model: 'cyzh', title: '' },
			questionLoading: false,
			questionsNum: 0,
			checkList: [], // 选中的知识库
			knowledge: [], // 知识库
			knowledgeBase: false, // 知识库显示隐藏
			sendType: 1,
			dataDrillingList: [],
			plugins: [],
			showMode: 'cyzh,zdbk,zklh,sjzx',
			coosZnwsKey: 1,
			dialogVisible: false
		};
	},
	computed: {
		...mapGetters(['rentInfo', 'aiEnterList']),
		rightBackground() {
			return this.extend?.background || '';
		},
		// 显示一些功能
		pluginOrDataDrilling() {
			return this.currentMode.permsCode === getDictionary('AI编码/智控领航')
				? this.plugins
				: this.dataDrillingList;
		},
		// 第一条消息是答案的时候加上间距
		FirstAnswerMargin() {
			return this.messageList.length > 0 && this.messageList[0].type === 'answer' ? '24px' : 0;
		}
	},
	watch: {
		sessionId(val, oldVal) {
			if (val && !oldVal) {
				//  更新历史记录
				this.getChatRecords();
			}
		},
		// 只要当前模式的按钮存在办公助手，则默认选中
		currentModeButtons(val, oldVal) {
			if (val.includes('administrativeAssistant')) {
				this.searchType = 'administrativeAssistant';
			}
		}
	},
	created() {
		// 这里没有这个modeType参数岂不是请求的废数据，即使有，currentMode对象也不完整
		// this.currentMode.modeType = this.$route.query.modeType;
		// this.getPluginData();
		// this.getKnowledge(); // 获取知识库
		// this.getQuestionList(); //获取问题
		// this.getDataDrilling(); // 获取数据智析的功能
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			if (['transcribeFile', 'transcribeFileDetails'].includes(from.name)) {
				vm.isProspectiveVersion = true;
				vm.prospectiveVersionType = 'recordingTranscript';
				// 添加 modeType 查询参数
				const query = { ...to.query, modeType: 'cyzh' };
				vm.$router
					.replace({
						name: to.name,
						params: to.params,
						query: query
					})
					.catch(err => {
						console.warn('重复导航被忽略:', err);
					});
			}
		});
	},
	async mounted() {
		this.ugly = !!this.$route.query.ugly;
		this.uglyColor = this.$route.query.uglyColor
			? this.$route.query.uglyColor
			: this.ugly
			? '#6618cf'
			: '';
		this.standaloneDisplay = !!this.$route.query.standaloneDisplay;
		this.individuallyAi = !!this.$route.query.individuallyAi;
	},

	beforeDestroy() {},
	methods: {
		getDictionary,
		/**重置前瞻版*/
		resetVersion() {
			this.isProspectiveVersion = false;
			this.customGoBack();
		},
		/**组件返回*/
		customGoBack() {
			this.prospectiveVersionType = '';
			this.prospectiveVersionUrl = '';
		},
		handleSearchFile(item) {
			this.dialogVisible = false;
			this.$refs.documentContent.getSearchList(item);
		},
		searchDialog() {
			this.dialogVisible = true;
		},
		/**改变前瞻版类型*/
		changeType({ openType, url }) {
			if (openType === 2) {
				window.open(/^http/.test(url) ? url : `${window.location.href.split('#')[0] + '#' + url}`);
			} else {
				if (/^innerComponents/.test(url)) {
					this.prospectiveVersionType = url.split('?type=')[1];
				} else {
					this.prospectiveVersionType = 'webviewBox';
					this.prospectiveVersionUrl = url;
				}
			}
		},
		// 按钮标签点击
		handleButton(type) {
			if (!type) {
				return;
			}
			let data = this.extend.buttonsConfig[type];
			if (data) {
				this.changeType(data);
			}
		},
		/**去往前瞻版*/
		toNew() {
			this.isProspectiveVersion = !this.isProspectiveVersion;
		},
		selectChange(index, tag) {
			this.$refs.documentContent.tagMenuChange(index, tag);
		},
		/**获取文件背景图*/
		getBackground,
		/**清空文件上下文*/
		clearFile() {
			this.fileQuery = null;
		},
		/**
		 * @Desc 切换搜索
		 * @Param {String} type 选中的类型
		 * */
		internetSearch(type, mode) {
			if (!type) {
				return;
			}
			// 深度思考切换逻辑
			if (type === 'deepThink') {
				this.deepThink = !this.deepThink;
				// 如果切换到深度思考，则关闭联网搜索
				if (this.searchType === 'IsInternetSearch') {
					this.searchType = '';
				}
			} else if (type === 'currentThinkMode') {
				this.deepThink = false;
				this.searchType = '';
			}
			// 相同类型切换时取消选择
			else if (this.searchType === type) {
				this.searchType = '';
			}
			// 选择新类型
			else {
				this.searchType = type;
				// 如果选择联网搜索则关闭深度思考
				if (type === 'IsInternetSearch') {
					this.deepThink = false;
				}
			}
			// 只要不是切换的思考模式，就要重置
			this.currentThinkMode = type === 'currentThinkMode' ? mode : 'base';
			// 不管点击什么都关闭选择知识库弹窗
			this.knowledgeBase = false;
		},
		/**选择数据智析的业务*/
		selectDataDrilling(item) {
			this.currentDataDrilling = item;
		},
		refresh() {
			this.messageList.splice(-2, 2);
			this.send('', this.inputMessage, false);
		},
		sendInputMessage() {
			this.sendCon = this.inputMessage;
		},
		/**获取知识钻取的功能*/
		getDataDrilling() {
			getDataDrilling({ pageNo: 1, pageSize: 5 }).then(res => {
				if (res.code === 200) {
					this.dataDrillingList = res.result.records || [];
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**获取功能的数据*/
		getPluginData() {
			somePlugins({ clientType: '1' }).then(res => {
				this.plugins = res.result;
			});
		},
		/**继续在全部知识库中查找*/
		continueQuery(message) {
			let { answerId } = this.addTemporaryMessage('在全部知识库中查询'); // 添加消息队列
			// 区分智控模式和普通对话模式
			this.getQuery(answerId, message, true); // 异步获取消息回复
		},
		/**右键菜单*/
		contextmenu(event, item, index) {
			this.clickRightMessage = item;
			this.menuIndex = index;
			// 根据需要进行位置调整等操作
			this.menuLeft = event.clientX;
			this.menuTop = event.clientY;
			this.menuShow = true;
		},
		/**
		 * 添加消息
		 * @param {Object} answer 图表配置答案
		 * @param {Array} haveChats 历史对话的图标名称
		 * */
		addMessage(answer, haveChats = []) {
			let { answerId } = this.addTemporaryMessage('');
			let options;
			let name;
			if (Array.isArray(answer)) {
				options = answer[0].data;
				name = answer[0].name;
			} else {
				options = answer.data || answer;
				name = answer.name || '图表';
			}
			let newHaveChats = [...haveChats, name];
			this.messageList.forEach(item => {
				if (item.id === answerId) {
					item.answer = options;
					item.title = name;
					item.haveChats = newHaveChats;
					item.answerType = 'chart';
					item.isSpecialAnswer = true; // 答案是特殊渲染
					item.answerDone = true;
				}
			});
		},
		otherQuestions() {
			if (this.questionsNum > this.questionParams.pageNo * 4) this.questionParams.pageNo++;
			else this.questionParams.pageNo = 1;
			this.getQuestionList();
		},
		/**选择文件*/
		pickFile(type) {
			this.accept = '.pdf,.doc,.docx,.txt,.xlsx,.png,.jpg,.jpeg,.gif,.png,.jpg,.jpeg,.gif';
			this.$nextTick(() => {
				this.$refs['fileInput'].click();
			});
		},
		/**获取知识库*/
		getKnowledge() {
			getKnowledge().then(res => {
				if (res.code === 200) {
					this.knowledge = res.result || [];
					// 默认不选知识库
					this.checkList = this.checkList.length
						? this.checkList
						: this.knowledge.map(item => item.id);
				} else {
					this.$message.error(res.message);
				}
			});
		},
		// 推荐
		getQuestionList() {
			if (isEmpty(this.currentMode)) return;
			this.questionLoading = true;
			// type是唯一的，modeType可能不唯一，比如问答助手1和问答助手2
			this.questionParams.model = this.currentMode.modeType;
			getQuestionsList(this.questionParams).then(res => {
				if (res.code === 200) {
					this.questionsList = res.result.records;
					this.questionsNum = res.result.total;
				} else {
					this.$message.error(res.message);
				}
				this.questionLoading = false;
			});
		},
		/**更多应用点击之后的生成答案框*/
		addMoreApplicationAnswer(message, functionId) {
			let { answerId } = this.addTemporaryMessage('');
			this.getPluginAiAnswer(answerId, message, functionId);
		},
		/**选择文件完成*/
		onPickFile(event) {
			let file = event.target.files[0];
			let { name } = file;
			let typeArr = name.split('.');
			let type = typeArr[typeArr.length - 1];
			event.target.value = '';
			this.processStatus = '#19be6b';
			this.fileQuery = {
				name,
				type,
				process: 0
			};
			customUploadFileHandler({ file }, process => {
				// 根据定位的文件所在索引位置，赋值进度条
				this.fileQuery.process =
					((process.loaded / process.total) * 100).toFixed(2) > 99
						? 99
						: ((process.loaded / process.total) * 100).toFixed(2);
			}).then(res => {
				if (res.code === 200) {
					this.fileQuery.id = res.result.filenames;
					this.fileQuery.process = 100;
				} else {
					this.processStatus = '#ff0000';
					this.$message.error(res.message);
				}
			});
		},
		/**重置当前选中功能 */
		handlePluginReset() {
			if (this.currentMode.permsCode === getDictionary('AI编码/智控领航')) {
				this.currentPlugin = null;
				this.pluginUUID = null; // 清除智控对话id
			} else if (this.currentMode.permsCode === getDictionary('AI编码/数据智析')) {
				this.currentDataDrilling = null;
			}
		},
		/**滚动到最底部*/
		toBottom() {
			this.$nextTick(() => {
				let messageListElement = this.$refs.messageContent;
				if (messageListElement) {
					messageListElement.scroll({
						top: messageListElement.scrollHeight,
						left: 0,
						behavior: 'auto'
					});
				}
			});
		},
		/**点击推荐的功能或者数据智析*/
		clickSome(item) {
			if (this.currentMode.permsCode === getDictionary('AI编码/智控领航')) {
				this.selectPlugin(item);
			} else if (this.currentMode.permsCode === getDictionary('AI编码/数据智析')) {
				this.selectDataDrilling(item);
			}
		},
		// 更新选择模式
		setCurrentMode(obj) {
			try {
				this.resetVersion();
				this.extend = JSON.parse(obj.extend);
				// 模式相关的按钮
				this.currentModeButtons = this.extend.buttons || [];
				// 知识库是否隐藏菜单
				if (this.extend.hideKnowledgeMenu === 'true') {
					this.$nextTick(() => {
						this.selectChange('3');
					});
				}
			} catch (e) {
				this.currentModeButtons = [];
			}
			this.currentMode = obj;
			this.reset(true, 1);
			// 获取智控领航的数据
			if (obj.permsCode === getDictionary('AI编码/智控领航')) {
				this.getPluginData();
			}
			// 获取知识库
			if (
				this.currentModeButtons.includes('defaultKnowledge') ||
				this.currentModeButtons.includes('knowledgeBase')
			) {
				this.getKnowledge();
			}
			//获取问题
			this.getQuestionList();
			// 获取数据智析的功能
			if (obj.permsCode === getDictionary('AI编码/数据智析')) {
				this.getDataDrilling();
			}
		},
		/**选择功能*/
		async selectPlugin(item) {
			if (this.currentPlugin && item.id === this.currentPlugin.id) return;
			try {
				// type为3的时候，为打开应用，直接提示跳转
				if (item.type === 3) {
					this.$confirm('请确认是否打开此应用?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							let { extend, name, logoUrl, isExternal } = item;
							extend = JSON.parse(extend);
							let page = extend.PC;
							if (isExternal) {
								let url = page || '';
								let mainUrl = /http/gi.test(url) ? url : (preUrl || window.location.origin) + url;
								if (extend.openType === 2) {
									window.open(mainUrl);
								} else {
									this.$router.push(
										`/other-system?url=${encodeURIComponent(
											mainUrl || ''
										)}&name=${name}&logoUrlPath=${logoUrl}`
									);
								}
							} else {
								this.$router.push(page);
							}
						})
						.catch(() => {});
					return;
				}
				this.pluginUUID = null; // 清除智控对话id
				this.currentPlugin = item;
				let uuidRes = await getUUID();
				if (uuidRes.code !== 200) {
					throw new Error(uuidRes.message);
				}
				this.pluginUUID = uuidRes.result[0]; // 智控模式单独存储
				// 选择功能之后第一句提示语
				if (item.prologue) {
					let FirstAnswerId = this.addTemporaryMessage('').answerId;
					this.messageList.forEach(message => {
						if (message.id === FirstAnswerId) {
							message.answer = item.prologue;
							message.answerDone = true;
						}
					});
				}
				// type为2的时候是查询列表，只提示，不回答
				// type为1展示初始表单
				if (item.type === 1) {
					// 选择功能展示初始表单
					let { answerId } = this.addTemporaryMessage('');
					let data = {
						dialogChatId: this.pluginUUID,
						functionId: item.id,
						sessionId: this.sessionId
					};
					let res = await getChangePluginInit(data);
					if (res.code !== 200) {
						throw new Error(res.message);
					}
					// 更新sessionId
					if (res.result.sessionId) {
						this.sessionId = res.result.sessionId;
					}
					let result = res.result.answer;
					if (/^centerControlDataJson:/.test(result)) {
						let result_json = result.split('centerControlDataJson:')[1];
						result_json = JSON.parse(result_json);
						// result_json.data = JSON.parse(result_json.data)
						this.messageList.forEach(message => {
							if (message.id === answerId) {
								message.answer = result_json;
								message.isSpecialAnswer = true; // 答案是特殊渲染
								message.answerDone = true;
							}
						});
					} else {
						let arr = result.split('');
						let i = 0;
						this.messageList.forEach(item => {
							if (item.id === answerId) {
								let T = setInterval(() => {
									if (i === arr.length) {
										item.answerDone = true;
										clearInterval(T);
									} else {
										item.answer += arr[i];
										i++;
										if (!this.lockAutoScroll) {
											this.toBottom();
										}
									}
								}, 50);
							}
						});
					}
				}
				if (!this.lockAutoScroll) {
					this.toBottom();
				}
			} catch (err) {
				this.$message.error(err.message || '请求超时');
			}
		},

		/**监听滚动*/
		scroll(e) {
			let event = e.srcElement;
			this.contentEl = event;
			this.scrollLoad();
			if (event.scrollTop + event.clientHeight >= event.scrollHeight - 10) {
				this.lockAutoScroll = false;
			} else {
				this.lockAutoScroll = true;
			}
		},
		/**滚动加载*/
		scrollLoad: debounce(
			function () {
				if (!this.loading && !this.lock && this.contentEl.scrollTop < 10) {
					this.params.pageNo += 1;
					this.getCoosMessage();
				}
			},
			500,
			false
		),
		boxScrollLoad: debounce(
			function () {
				if (!this.loading && !this.lock) {
					this.params.pageNo += 1;
					this.getCoosMessage();
				}
			},
			500,
			false
		),
		/**查看更多功能*/
		lookMore() {
			if (this.currentMode.permsCode === getDictionary('AI编码/智控领航')) {
				this.$refs.PluginPopup.open();
			} else if (this.currentMode.permsCode === getDictionary('AI编码/数据智析')) {
				this.$refs.dataDrillingPopup.open();
			}
		},
		/**清空选中*/
		clearCheck() {
			this.$message.warning('该模式下至少需要选中一个知识库！');
			this.checkList = [this.knowledge[0].id];
		},
		/**清空消息队列*/
		clear() {
			this.reset();
			// setTimeout(() => {
			// 	this.lock = false;
			// }, 3000);
		},

		/**创建新对话*/
		createNewChat() {
			this.$confirm(`确定创建新对话吗?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					if (
						[getDictionary('AI编码/智能问数H5'), getDictionary('AI编码/智能问政H5')].includes(
							this.currentMode.permsCode
						)
					) {
						this.$refs.H5.reset();
					} else if ([getDictionary('AI编码/问答助手')].includes(this.currentMode.permsCode)) {
						this.coosZnwsKey += 1;
					} else {
						this.reset(true, 1);
					}
				})
				.catch(() => {});
		},
		/**重置数据*/
		reset(reload = true, type) {
			this.fileQuery = null; // 清空基于文件对话的文件
			this.messageList = [];
			this.lock = true;
			this.deepThink = false;
			this.IsSend = false;
			this.isPause = false;
			if (type) {
				// 新增对话
				this.sessionId = '';
			}
			this.params.pageNo = 1;
			if (this.currentMode.permsCode === getDictionary('AI编码/智答宝库')) {
				this.checkList = this.knowledge.map(item => item.id);
			} else {
				this.checkList = []; // 清空知识库选中
			}
			this.currentPlugin = null; // 清除当前选中的功能
			this.currentDataDrilling = null; // 清除当前选中的功能
			this.pluginUUID = null; // 清除智控对话id
			if (this.currentMode.permsCode === getDictionary('AI编码/ppt')) {
				this.$nextTick(() => {
					this.$refs.pptCoos.resetMessageList(1);
				});
			}
			if (this.currentMode.permsCode === getDictionary('AI编码/智能问数')) {
				this.$nextTick(() => {
					this.$refs.analysis.resetMessageList(1);
				});
			}
		},
		/**演示：改变用户权限*/
		changeRole(role) {
			this.currentVersion = role;
		},
		/**改变会话id*/
		changeSessionId(id, type) {
			if (this.currentMode.permsCode === getDictionary('AI编码/智能问数')) {
				this.$refs.analysis.changeSessionId(id, type);
				return;
			}
			this.sessionId = id;
			this.messageList = [];
			this.fileQuery = null;
			this.params.pageNo = 1;
			this.lock = false;
			this.getCoosMessage(type);
		},
		/**获取coos的历史聊天记录*/
		async getCoosMessage(type) {
			let params = {
				pageNo: this.params.pageNo,
				pageSize: this.params.pageSize
			};
			if (this.$refs.coosZnws) {
				this.$refs.coosZnws.pause();
			}
			// 全局组件不是单独针对某种模式的，只是把页面当成了组件使用
			if (this.isComponent && this.componentType !== 'glob') {
				params.chatType =
					this.componentType === 'wait' ? '2' : this.componentType === 'other-system' ? '3' : ''; // 组件模式2为待办，3为其他系统的列表
				params.chatObjectId = this.sessionId;
			} else {
				params.sessionId = this.sessionId;
			}
			if (this.coosType === 'space') {
				params.chatType = '5';
			}
			if (this.coosType === 'waitList') {
				params.chatType = '4';
			}
			// if (params.chatType === '3') return;
			if (!params.chatType) {
				// params.sessionTypes = '1,2,3';
			}
			this.loading = true;
			if (this.$route.query.viewAllHistory && this.$route.query.viewAllHistory === 'true') {
				params.viewAllHistory = true;
			}
			let res = await getCoosHistory(params);
			if (res.code === 200) {
				if (this.params.pageNo >= res.result.pages) {
					this.lock = true;
				}
				if (type && type == 101) {
					// ppt
					let data = res.result.records[0];
					this.$refs.pptCoos.setAnswer(data.answer, data.query);
					return;
				}
				let message = [];
				res.result?.records
					.sort((a, b) => {
						return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
					})
					.forEach(item => {
						message.push({
							type: 'query',
							query: item.query,
							createTime: item.createTime
						});
						let answer = item.answer;
						let answerType = 'normal';

						let isSpecialAnswer = false;
						try {
							// jxj 问答模式 历史处理
							answer = JSON.parse(answer).dataJxjInterpretationJson;
							let data = { ...answer, answerDone: true };
							// 处理来源
							if (data.recommendAgent && data.recommendAgent.length) {
								data.recommendAgent.forEach(agent => {
									if (data['sourceFile' + agent.id] && data['sourceFile' + agent.id].length) {
										data['answerFrom' + agent.id] = data['sourceFile' + agent.id];
									}
								});
							}
							message.push(data);
						} catch (e) {
							if (/^centerControlDataJson:/.test(answer)) {
								answer = answer.split('centerControlDataJson:')[1];
								answerType = Array.isArray(answer) ? 'MULTI' : answer.functionType; // 1是表单模式  2数据列表模式  3应用  MULTI组合模式
								answer = answerType === 3 ? [JSON.parse(answer)] : JSON.parse(answer); // 应用模式转成数组按照组合模式进行渲染
								// answer.data = JSON.parse(answer.data)

								isSpecialAnswer = this.getIsSpecial(answer); // 答案是特殊渲染
							}
							// 获取知识库来源
							let answerFrom = item.answerFrom ? JSON.parse(item.answerFrom) : [];
							// 处理来源中的链接
							answerFrom = answerFrom.map(form => {
								if (form.disk_file && form.disk_file.links) {
									let links = JSON.parse(form.disk_file.links);
									form.disk_file.links = links.filter(link => {
										return link.pc;
									});
									form.openLink = true; // 如果有三方链接，设一个收起打开状态
								}
								return form;
							});
							// 获取推荐问题
							let recommendedQuestions = answerFrom.reduce((base, form) => {
								base = base.concat(form.questions || []);
								return base;
							}, []);
							let data = {
								...item,
								type: 'answer',
								answer,
								answerType,
								title: this.getSpecialTitle(answerType),
								isSpecialAnswer, // 答案是特殊渲染
								isHistory: true, // 加一个历史记录的标识
								answerFrom,
								isDgg: false, // 是否点赞（经信局才有）
								answerDone: true
							};
							// 如果有推荐问题
							if (recommendedQuestions.length) {
								data.recommendedQuestions = [...new Set(recommendedQuestions)].slice(0, 3);
							}
							// 看历史记录是否有深度思考内容
							if (/(<think>|<\/think>)/gi.test(answer)) {
								data.isThink = true;
								let i = answer.indexOf('</think>');
								// 思考结束前属于思考过程 (有可能没有结束标记)
								data.thinkCon = i > -1 ? answer.slice(0, i).replace(/<think>/gi, '') : '';
								// 思考结束后属于答案 (有可能没有结束标记，但以下写法均兼容)
								data.answer = answer.slice(i + 8);
								data.startThink = false;
							}
							// 新版本，支持各种答案混合渲染，可能都是通过流式慢输出的字符串，自己解析
							if (typeof data.answer === 'string') {
								data.answerMulti = this.handleMultiAnswer(data.answer, item.id);
							}
							message.push(data);
						}
					});
				this.messageList = message.concat(this.messageList);
				// 所有项目的历史记录都要
				let { result } = await existsDgg(res.result.records.map(item => item.query));
				message.forEach(item => {
					if (item.type === 'answer') {
						item.isDgg = result[item.query];
					}
				});
				if (this.$refs.coosZnws) {
					this.$refs.coosZnws.modelId = '';
					this.$refs.coosZnws.messageList = this.messageList;
					this.$refs.coosZnws.sessionId = this.sessionId;
					this.$refs.coosZnws.messageId = this.messageList.length + 1;
					if (this.params.pageNo === 1) {
						this.$refs.coosZnws.toBottom();
					}
				}
				if (this.params.pageNo === 1) {
					this.toBottom();
				}
			} else {
				this.$message.error(res.message);
			}
			this.loading = false;
		},
		/**发送消息*/
		sendMessage(e) {
			if (this.sendType === 1 && e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
				this.send();
			}
			if (this.sendType === 2 && e.key === 'Enter' && e.ctrlKey) {
				this.send();
			}
		},
		// 暂停
		pause() {
			try {
				this.controller.abort();
			} catch (e) {
				console.log(e);
			}
			this.controller = null;
			this.isPause = true;
			this.messageList[this.messageList.length - 1].answerDone = true;
			this.IsSend = false;
		},
		getChatRecords() {
			this.$refs.CoosAiLeft.resetList();
		},
		/**
		 * @description 发送消息
		 * @param {Object} e 点击元素
		 * @param {Object} message 发送的消息（默认输入框输入的）可供全局调用的通用方法
		 * @param clearType  是否清空输入框
		 * */
		send(e, message = this.sendCon, clearType = true) {
			if (this.IsSend) {
				return;
			}

			this.isPause = false;
			if (message.replace(/(\n|\s)/gi, '')) {
				this.inputMessage = message;
				this.IsSend = true;
				if (clearType) {
					this.sendCon = ''; // 清空
				}
				let { answerId } = this.addTemporaryMessage(message); // 添加消息队列
				this.getAiAnswer(answerId, message);
			} else {
				this.sendCon = '';
				this.$message.error('发送消息不能为空！');
			}
		},
		/**
		 * @method 添加临时消息
		 * @param {String} message 追加消息
		 * @param {Boolean} addAnswer 是否追加答案
		 * */
		addTemporaryMessage(message, addAnswer = true) {
			let answerId = 'coos-answer-' + this.messageId;
			let queryId = 'coos-query-' + this.messageId;
			// 添加自己发送的消息
			if (message) {
				this.messageList.push({
					type: 'query',
					id: queryId,
					query: message,
					createTime: new Date().toLocaleString()
				});
			}
			// 添加答案占位消息
			if (addAnswer) {
				this.messageList.push({
					type: 'answer',
					isAll: true, // 是否在全部知识库查询
					query: message, // 问题
					answerFrom: [], // 文档来源
					answerFromDone: this.checkList.length === 0, // 文档来源数据加载完毕
					id: answerId, // 问答ID
					startThink: false, // 是否开始思考
					isThink: false, // 是否深度思考
					thinkCon: '', // 思考内容
					answer: '', // ai答案
					isDgg: false, // 是否点赞（经信局才有）
					answerMulti: null, // 组合答案
					answerDone: false // 答案数据加载完毕
				});
			}
			this.messageId++;
			this.toBottom();
			return { queryId, answerId };
		},
		/**阻止回车默认行为*/
		preventEnter(e) {
			if (!e.shiftKey && e.key === 'Enter') {
				e.preventDefault();
			}
		},
		/**显示知识库选择*/
		showKnowledgeBase() {
			this.knowledgeBase = !this.knowledgeBase;
			this.showModeChange = false;
		},
		closeKnowledgeBase() {
			this.knowledgeBase = false;
		},
		handleSendType(item) {
			this.sendType = item;
			this.$refs.popover.doClose();
		}
	}
};
</script>

<style scoped lang="scss">
.handle-icon {
	font-size: 16px;
	color: #15224c;
	margin-left: 4px;
	cursor: pointer;
}
.im-content {
	flex: 1;
	height: 100%;
	overflow: hidden;
	border-radius: 12px;
	background: #fcfcfc;
	display: flex;
	.left {
		background: #f3f4f6;
		max-width: 280px;
	}
	.right {
		position: relative;
		flex: 1;
		@include flexBox();
		.to-new {
			position: absolute;
			top: 60px;
			right: 60px;
			cursor: pointer;
			color: var(--brand-6);
			@include flexBox();
			&-icon {
				font-size: 24px;
				margin-right: 4px;
			}
		}
		.right-box {
			overflow: hidden;
			height: 100%;
			width: 75%;
			display: flex;
			flex-direction: column;
			min-width: 665px;
			&-item {
				display: flex;
				overflow-y: auto;
				flex: 1;
				justify-content: center;
				flex-direction: column;
				@include noScrollBar();
			}
			@include noScrollBar();
			.right-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 30px;
				color: #222222;
				line-height: 42px;
				text-align: center;
			}
			.right-dec {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 18px;
				color: #6a7b9e;
				line-height: 40px;
				text-align: center;
			}
		}
		.message {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: column;
			position: relative;
			overflow: hidden;
			&-utils {
				width: 100%;
				height: 42px;
				padding: 6px 10px;
				position: absolute;
				left: 0;
				bottom: 0;
				display: flex;
				align-items: center;
				.more-icon {
					cursor: pointer;
					margin-right: 9px;
				}
				.send-pause {
					position: absolute;
					right: 10px;
					top: 8px;
					.send-pause-send-icon {
						cursor: pointer;
						width: 30px;
						height: 30px;
						background: #d1d3db;
						border-radius: 18px 18px 18px 18px;
						color: #fff;
						@include flexBox();
					}
					.active-send-icon {
						background: var(--brand-6);
					}
					.send-pause-pause-icon {
						width: 30px;
						height: 30px;
						background: var(--brand-6);
						box-shadow: 0px 4px 4px 0px rgba(61, 159, 245, 0.25);
						@include flexBox();
						border-radius: 18px 18px 18px 18px;
						.pause-icon {
							width: 10px;
							height: 10px;
							background: #ffffff;
							border-radius: 3px 3px 3px 3px;
						}
					}
				}
				&-mode {
					display: inline-flex;
					align-items: center;
					background: #f5f7fa;
					border-radius: 3px;
					padding: 5px 8px;
					font-weight: 400;
					font-size: 12px;
					color: $textColor;
					line-height: 20px;
					text-align: center;
					font-style: normal;
					text-transform: none;
					margin-right: 8px;
					cursor: pointer;

					&-icon {
						width: 20px;
						height: 20px;
						margin-right: 2px;
					}
				}

				&-select {
					background: #f5f7fa;
					padding: 4px 8px;
					border-radius: 6px;
					border: 1px solid #f5f7fa;
					display: inline-flex;
					align-items: center;
					margin-right: 8px;
					.plugin-choose {
						display: flex;
						align-items: center;
					}

					.icon {
						font-size: 20px;
						margin-right: 2px;
					}

					.zklh-icon {
						font-size: 20px;
						margin-right: 2px;
						color: #035dff;
					}

					.text {
						font-size: 12px;
						font-weight: 400;
						color: $textColor;
						line-height: 20px;
						cursor: pointer;
					}

					.plugin-text {
						color: var(--brand-6);
						margin: 0 5px;
					}

					.plugin-reset {
						margin-left: 20px;
						font-size: 12px;
						color: var(--brand-6);
						line-height: 20px;
					}
				}

				.quick-button {
					position: absolute;
					top: calc(50% - 12px);
					right: 20px;
					cursor: pointer;
					@include flexBox();

					&-icon {
						margin-left: 12px;
						font-size: 20px;
					}
				}

				.mode-popup {
					position: absolute;
					bottom: calc(100% + 17px);
					left: 13px;
					min-width: 163px;
					height: auto;
					border-radius: 12px;
					background: #ffffff;
					box-shadow: 0px 4px 14px 0 rgba(0, 0, 0, 0.1);
					@include flexBox(flex-start, flex-start);
					flex-direction: column;

					.mode-title {
						width: 100%;
						font-weight: 500;
						font-size: 14px;
						color: $primaryTextColor;
						line-height: 22px;
						text-align: center;
						font-style: normal;
						text-transform: none;
						padding: 10px 10px 5px;
						border-bottom: 1px solid #f0f0f0;
					}

					.mode-list {
						width: 100%;
						padding: 0 8px 8px;

						&-item {
							padding: 9px 8px;
							cursor: pointer;
							@include flexBox(flex-start);

							&-img {
								height: 20px;
								width: 20px;
								margin-right: 3px;
							}

							&-text {
								font-weight: 400;
								font-size: 14px;
								color: $textColor;
								line-height: 22px;
								text-align: left;
								font-style: normal;
								text-transform: none;
							}
						}
					}
				}
			}
			&-send {
				position: relative;
				margin-top: 30px;
				min-height: 135px;
				padding: 20px 0 10px;
				background: #ffffff;
				box-shadow: 0px 4px 5px 0px rgba(21, 34, 76, 0.06);
				border-radius: 16px 16px 16px 16px;
				border: 1px solid #d9e2ec;
				&-input {
					height: 100%;
					::v-deep .el-textarea__inner {
						height: 100% !important;
						overflow-y: auto;
						border-radius: 16px 16px 16px 16px;
						background: #ffffff;
						border: none;
						&::placeholder {
							color: $holderTextColor;
						}

						@include noScrollBar;

						&:focus {
							border-color: transparent;
							box-shadow: none;
							@include scrollBar;
						}

						&:hover {
							border-color: transparent;
							box-shadow: none;
							@include scrollBar;
						}
					}
				}
			}

			.menu {
				position: fixed;
				background: #ffffff;
				border-radius: 3px;
				padding: 5px 0;
				width: 70px;
				text-align: center;
				box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.3);

				&-item {
					padding: 8px 0;
					font-size: 12px;
					cursor: default;

					&:hover {
						background: #f0f0f0;
					}
				}
			}
		}
	}
}
.questions {
	width: 100%;
	max-width: 632px;
	margin-top: 15px;

	&-container {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
	}

	&-item {
		background: #ffffff;
		border-radius: 6px 6px 6px 6px;
		border: 1px solid #e4eaf5;
		cursor: pointer;
		padding: 10px 12px;
		border-radius: 6px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: $primaryTextColor;
		line-height: 24px;
	}

	&-title {
		width: 100%;
		display: flex;
		margin-bottom: 8px;
		justify-content: space-between;

		& label {
			font-family: PingFang SC, PingFang SC;
			height: 24px;
			font-weight: 400;
			font-size: 14px;
			color: $subTextColor;
			line-height: 24px;
		}

		&-other {
			cursor: pointer;
			height: 24px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 12px;
			color: var(--brand-6);
			line-height: 24px;
			display: flex;
			align-items: center;

			& i {
				margin-right: 10px;
			}
		}
	}
}
.message-list {
	overflow-y: auto;
	flex: 1;
	@include flexBox(flex-start, flex-start);
	flex-direction: column;
	@include noScrollBar;
}
.IsInternetSearch {
	color: var(--brand-6) !important;
}
.IsInternetSearch-box {
	background: var(--brand-1) !important;
	border: 1px solid var(--brand-3) !important;
}
.file-tip {
	margin-top: 30px;
	//position: absolute;
	//top: 0;
	//left: 0;
	width: 100%;
	background: rgba(172, 200, 255, 0.24);
	padding: 8px 20px;
	@include flexBox(space-between);

	.file-tip-left {
		flex: 1;
		overflow: hidden;
		@include flexBox(flex-start);

		.file-tip-image {
			width: 24px;
			height: 24px;
			flex-shrink: 0;
		}

		.file-tip-text {
			font-size: 14px;
			flex-shrink: 0;
		}

		.file-tip-name {
			max-width: 400px;
			font-size: 14px;
			@include aLineEllipse;
			color: var(--brand-6);
		}
	}

	.file-tip-btn {
		cursor: pointer;
		color: var(--brand-6);
	}

	.file-tip-process {
		position: absolute;
		bottom: 0;
		width: 100%;
	}
}
.pause {
	margin-top: 17px;
	width: 100%;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #737a94;
	height: 22px;
	line-height: 22px;
	text-align: center;
	&-text {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #2778e5;
		line-height: 22px;
		text-align: center;
		cursor: pointer;
		margin-right: 10px;
	}
}
.deepThink {
	font-size: 16px !important;
}
.zhishiku {
	font-size: 22px !important;
}
.folder-file-icon {
	font-size: 20px !important;
}
.dropdown-link {
	cursor: pointer;
}
.think-mode {
	padding: 4px 18px;
}
.current-think-mode {
	background: var(--brand-6) !important;
	color: #ffffff;
}
::v-deep .el-dropdown-menu__item {
	padding: 0 !important;
}
::v-deep .el-popper {
	padding: 0 !important;
	margin: 0 !important;
}
.knowledge-base {
	width: 339px;
	border-radius: 12px;
	background: #ffffff;
	padding: 12px;
	box-shadow: 0px 4px 14px 0 rgba(0, 0, 0, 0.1);
	@include flexBox(flex-start, flex-start);
	flex-direction: column;
	max-height: 600px;
	&-title {
		width: 100%;
		flex-shrink: 0;
		height: 44px;
		@include flexBox(flex-start);
		font-size: 16px;
		font-weight: 400;
		color: $subTextColor;
		line-height: 22px;
		position: relative;

		.data {
			font-size: 16px;
			font-weight: 400;
			color: $primaryTextColor;

			&-select {
				color: var(--brand-6);
			}
		}

		& > .button {
			font-size: 14px;
			font-weight: 400;
			color: var(--brand-6);
			line-height: 22px;
			position: absolute;
			right: 8px;
			cursor: pointer;
		}
	}

	&-con {
		width: 100%;
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;

		@include noScrollBar;
	}

	&-item {
		height: 46px;
		width: 100%;
		padding: 0 8px;
		border-bottom: 1px solid #f0f0f0;
		overflow: hidden;
		@include flexBox(flex-start);

		::v-deep .el-checkbox__label {
			flex: 1;
			overflow: hidden;
			@include flexBox(flex-start);
		}

		.icon {
			font-size: 20px;
			line-height: 20px;
			margin-right: 3px;
		}

		.title {
			font-size: 14px;
			font-weight: 800;
			color: $textColor;
			line-height: 14px;
			@include aLineEllipse;
		}
	}
}
.component-content {
	width: 100%;
	height: 100%;
}
.ywxw {
	background: linear-gradient(120deg, #d2edff 0%, #ded7f5 100%) !important;
	border-radius: 6px;
	border: 1px solid #e4ecfd;
	cursor: pointer;
	span {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: #15224c;
		line-height: 20px;
		text-align: justify;
	}
}
.right-header {
	display: flex;
	justify-content: center;
}

.right-header-img {
	width: 100%;
}
.document-right {
	overflow: hidden;
}
.document-menu {
	width: 200px;
	height: 100%;
	flex-shrink: 0;
}
.bottom {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 13px;
	color: #5b6aa3;
	line-height: 18px;
	width: 100%;
	text-align: center;
	padding: 18px 0;
}
</style>
<style>
.knowledge-base-parent {
	padding: 0;
	margin: 0;
	border-radius: 12px;
}
</style>
