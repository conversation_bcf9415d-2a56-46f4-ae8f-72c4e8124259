<template>
	<div class="box">
		<!-- 左侧内容 -->
		<div v-loading="departLfLoading" class="lf">
			<!-- 组织结构数 -->
			<departmentTree
				:data="departmentData"
				label="orgName"
				@handleNode="handleDepartmentOrg"
			></departmentTree>
		</div>
		<div v-loading="rfLoading" class="rf">
			<div class="dep-box">
				<div class="head">
					<!-- 搜索框 -->
					<SearchFormRender
						ref="vFormRefSearch"
						class="searchBox"
						is-search-views
						:form-json="{ widgetList: selectedQueryColumns }"
						@searchClick="handleSearch"
						@resetClick="handleReset"
					></SearchFormRender>
					<div class="headTitle">
						<div class="btn-box">
							<el-button type="primary" icon="el-icon-plus" @click="openDrawer">新建部门</el-button>
						</div>
					</div>
				</div>
				<div class="list">
					<depDynamicList
						ref="list"
						@selectedQueryColumns="selectedQueryColumnsFun"
					></depDynamicList>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import DepartmentTree from '@/components/department-tree/index.vue';
import SearchFormRender from '@/components/search-form-render/index.vue';
import DepDynamicList from '@/components/dep-dynamic-list/index.vue';
import { getDepartmentTree } from '@/api/modules/dynamic';

export default {
	components: { DepDynamicList, SearchFormRender, DepartmentTree },
	data() {
		return {
			rfLoading: false,
			departLfLoading: false,
			depSearch: '',
			date: '',
			selectedQueryColumns: [],
			param: null,
			title: '',
			orgId: '',
			searchId: null,
			departmentData: []
		};
	},
	created() {
		// 部门树
		this.getDepartmentTree();
	},
	methods: {
		// 获取部门树组件数据
		getDepartmentTree(includeDepartment) {
			this.departLfLoading = true;
			getDepartmentTree({ includeDepartment, limitOrg: true }).then(res => {
				if (res.code === 200) {
					this.departmentData = res.result;
					this.$forceUpdate();
				}
				this.departLfLoading = false;
			});
		},
		handleDepartmentOrg(i) {
			console.log(i, '111');
			let { id, title } = i;
			this.title = title;
			this.orgId = id;
			// 分页重制
			this.$refs.list.pageNo = 1;
			// 刷新列表数据
			this.$refs.list.orgId = id;
			this.searchId = id;
			this.$refs.list.getDepartsRootSearch();
		},
		selectedQueryColumnsFun(data) {
			this.selectedQueryColumns = data;
			if (this.$refs.vFormRefSearch) {
				this.$refs.vFormRefSearch.setFormJson({ widgetList: data });
			}
		},
		handleSearch(data) {
			this.param = data;
			this.page = 1;
			this.$refs.list.getDepartsRootSearch(data, this.page);
		},
		handleReset(data) {
			this.param = data;
			this.page = 1;
			this.$refs.list.getDepartsRootSearch(data, this.page);
		},
		openDrawer() {
			this.$refs.list.openDrawer('', this.orgId);
		}
	}
};
</script>
<style scoped lang="scss">
@import '~@/views/departments-members/components/index.scss';
.btn-box {
	justify-content: flex-end;
}
.dep-box {
	display: flex;
	flex-direction: column;
	flex: 1;
	padding-top: 20px;
	.list {
		flex: 1;
	}
	.headTitle {
		padding: 16px 16px 0 0;
	}
}
</style>
