<template>
	<div id="EditorContent" v-loading="loading" class="step3-page">
		<div class="page-content">
			<div class="step3" :class="{ 'step3-showSource': !openPopup && !aiShow && !openError }">
				<div class="step3-left" :class="{ 'step3-showSource': openPopup || aiShow || openError }">
					<coosTitle
						:id="id"
						:doc-title="docTitle"
						:done="done"
						:save-success="saveSuccess"
						v-bind="$attrs"
						v-on="$listeners"
					>
						<!--   按钮集合   -->
						<div slot="right" class="pro-buttons">
							<el-button
								v-if="!isHistory"
								type="primary"
								ghost
								class="pre-button"
								:disabled="!done"
								:class="{ 'disabled-button': !done }"
								@click="pre"
							>
								上一步
							</el-button>
							<el-button
								v-if="pathType !== 'error'"
								:class="{ 'disabled-button': !done || isError }"
								class="submit-button"
								:disabled="!done || isError"
								type="primary"
								@click="pro"
							>
								重新生成全文
							</el-button>
						</div>
					</coosTitle>
					<CoosEditor
						:id="id"
						:key="CoosEditorKey"
						ref="coosEditor"
						:doc-title="docTitle"
						:writee-type="writeeType"
						v-bind="$attrs"
						class="edit"
						:init-outline="outline"
						:done="done"
						:pending-text="pendingText"
						:content="content"
						v-on="$listeners"
						@pause="pause"
						@saveOutline="saveOutline"
						@openSetting="openSetting"
						@aiQuery="aiQuery"
						@changeTextHtml="changeTextHtml"
						@closeOther="closeOther"
						@updateSourceInfo="getSource"
						@changeSaveSuccess="changeSaveSuccess"
					></CoosEditor>
					<i
						v-show="openPopup || aiShow"
						class="coos-iconfont icon-tuozhuai drag-icon"
						@mousedown="startDrag"
					></i>
				</div>
				<sourceInfo
					v-show="openPopup"
					:width="rightWidth"
					:loading="getSourceStatus"
					:mark-content="markContent"
					:source-arr="sourceArr"
					:select-source-arr="selectSourceArr"
					@changeSourceSelect="changeSourceSelect"
					@queryAi="queryAi"
				></sourceInfo>
				<errorList
					v-show="openError"
					ref="errorList"
					@setErrorContent="setErrorContent"
					@toNewText="toNewText"
				/>
				<aiContent
					:id="id"
					ref="aiContent"
					:width="rightWidth"
					v-bind="$attrs"
					:ai-show="aiShow"
					@replace="replace"
				></aiContent>
				<div class="step3-right">
					<div
						v-for="(item, index) of utils"
						v-show="item.isShow"
						:key="index"
						class="step3-right-item"
						:class="{ 'select-util': currentUtil === item.type }"
						@click="handleUtils(item)"
					>
						<img
							:src="currentUtil === item.type ? item.iconS : item.icon"
							class="step3-right-item-icon"
							alt=""
						/>
						<div class="step3-right-item-text">{{ item.label }}</div>
					</div>
				</div>
			</div>
			<div v-show="showMore" class="pro-right">
				<setting
					ref="setting"
					:over-writ-content="overWritContent"
					:loading="saveLoading"
					v-bind="$attrs"
					@save="save"
					@cale="resetShow"
				></setting>
			</div>
		</div>
		<div
			v-if="showTip"
			class="tip"
			:class="{ 'level-status': canCloseStatus }"
			:style="hoverStyle"
			@mouseenter="enterPopup"
			@mouseleave="levelPopup"
		>
			<v-md-preview :text="hoverObj.page_content"></v-md-preview>
			<!--			<div class="tip-content" v-html="hoverObj.page_content"></div>-->
		</div>
	</div>
</template>
<script>
import CoosEditor from '@/views/draft-writing/components/editor.vue';
import {
	quicklyProOutline,
	getSourceDetail,
	saveOutline,
	generateFullText
} from '@/api/modules/coos-write';
import { Base64 } from 'js-base64';
import coosTitle from '@/views/draft-writing/components/title.vue';
import setting from '@/views/draft-writing/components/setting.vue';
import sourceInfo from '@/views/draft-writing/components/source.vue';
import aiContent from '@/views/draft-writing/components/ai.vue';
import { deepClone } from '@/utils';
import { mapMutations } from 'vuex';
import ErrorList from '@/views/draft-writing/components/error-list.vue';
import { proofreadTextSse } from '@/api/modules/error-correction';
const { quicklyProOutlineRequest, quicklyProOutlineCancel } = quicklyProOutline();
export default {
	name: 'Step3Index',
	components: {
		ErrorList,
		setting,
		coosTitle,
		CoosEditor,
		sourceInfo,
		aiContent
	},
	props: {
		docTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 写作类型
		writeeType: {
			type: String,
			default: () => {
				return '';
			}
		},
		isHistory: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			pendingText: '全文生成中...',
			cacheItem: '', // 生成全文缓存的流式数据
			getSourceStatus: false, // 获取信源状态
			reType: '', // 重写类型
			reData: null, // 重新请求的参数
			markContent: '', //高亮显示的内容
			canCloseStatus: false, // 可以关闭弹窗的状态
			startX: 0, // 拖拽初始位置
			initWidth: 0, // 开始拖拽时候初始宽度
			rightWidth: 463, // 右边弹性宽度
			hoverStyle: {},
			hoverObj: {}, // 临时的鼠标悬浮对象
			showTip: false, // 显示提示
			isError: false,
			saveSuccess: false,
			whetherToSkipOrNot: false, // 是否跳过大纲
			aiShow: false, // 智能对话弹窗显隐
			currentUtil: '',
			openPopup: false, // 信源弹窗,
			openError: false, // 纠错弹窗
			overWritContent: '',
			loading: false,
			saveLoading: false,
			showMore: false, // 显示标题大纲的更多设置
			sourceArr: [],
			originMarkInfo: {}, // 信源映射
			selectSourceArr: [], // 引用高亮显示的信源
			done: true,
			controller: null,
			content: '',
			T: null,
			CoosEditorKey: 1,
			id: '',
			outline: []
		};
	},
	computed: {
		pathType() {
			return this.$route.query.pathType;
		},
		utils() {
			let pathType = this.$route.query.pathType;
			return [
				{
					icon: require('@/assets/images/write/info-source.png'),
					iconS: require(`@/assets/${this.rentThem}/write/info-source-select.png`),
					label: '信源索引',
					isShow: pathType !== 'error',
					type: 'source'
				},
				{
					icon: require('@/assets/images/write/message-query.png'),
					iconS: require(`@/assets/${this.rentThem}/write/message-query-s.png`),
					label: '智能改写',
					isShow: pathType !== 'error',
					type: 'ai'
				},
				{
					icon: require('@/assets/images/write/error-correction-icon.png'),
					iconS: require('@/assets/images/write/error-correction-icon-s.png'),
					label: '文稿纠错',
					isShow: pathType === 'error',
					type: 'errorCorrection'
				}
			];
		}
	},
	mounted() {
		document.getElementById('EditorContent').addEventListener('keydown', event => {
			if ((event.ctrlKey || event.metaKey) && event.key === 's') {
				event.preventDefault();
				// 执行你的保存逻辑
			}
		});
	},
	methods: {
		...mapMutations('user', ['REMOVE_INFO']),
		/**信源点击问题发起的对话*/
		queryAi(query) {
			this.handleUtils({ type: 'ai' }, true);
			this.$refs.aiContent.queryAi(query);
		},
		/**外部使用-删除暂停*/
		delPause(id) {
			if (id === this.id) {
				this.id = '';
				this.pause();
			}
		},
		/**进入预览弹窗*/
		enterPopup() {
			this.canCloseStatus = false;
		},
		levelPopup() {
			this.canCloseStatus = true;
			setTimeout(() => {
				if (this.canCloseStatus) {
					this.hoverObj = {};
					this.showTip = false;
				}
			}, 500);
		},
		/**拖拽调整宽度*/
		startDrag(e) {
			e.preventDefault();
			this.startX = e.clientX;
			this.initWidth = this.rightWidth;
			document.addEventListener('mousemove', this.drag);
			document.addEventListener('mouseup', this.dragEnd);
		},
		drag(e) {
			e.preventDefault();
			this.rightWidth = this.rightWidth < 350 ? 350 : this.initWidth - (e.clientX - this.startX);
		},
		dragEnd() {
			document.removeEventListener('mousemove', this.drag);
			document.removeEventListener('mouseup', this.dragEnd);
			this.$refs.aiContent.updateRender();
		},
		// 重新纠错
		// 在 toNewText 方法中修改流式处理部分
		async toNewText(qualityTypes) {
			this.controller = new AbortController();
			const { signal } = this.controller;
			let aiRes;
			// 调用文稿校对接口
			try {
				aiRes = await proofreadTextSse(
					{
						id: this.id || this.$route.query.id,
						qualityTypes: qualityTypes
					},
					signal
				);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				this.$message.error(err.message || err);
				return;
			}
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			let buffer = ''; // 缓冲区
			let eventValue = '';
			let qualityGroup = '';
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				if (done) break;
				let res = decoder.decode(value);
				buffer += res;
				// 按行分割缓冲区内容
				const lines = buffer.split('\n');
				// 保留最后一行（可能不完整）在缓冲区中
				buffer = lines.pop();
				// 处理完整的行
				for (const line of lines) {
					// 处理event行
					if (line.startsWith('event:')) {
						eventValue = line.match(/event:(.*)/)?.[1] || '';
					}
					// 跳过空行和非data行
					if (!line.trim() || !line.startsWith('data:')) {
						continue;
					}
					const dataStr = line.substring(5); // 去掉 "data:" 前缀
					try {
						// 检查是否为错误响应
						if (/"code":50/.test(dataStr)) {
							let r = JSON.parse(dataStr);
							const errorMsg = r.message || '服务器异常';
							this.$message.error(errorMsg);
							return;
						} else if (/"code":401/.test(dataStr)) {
							const errorMsg = '登录失效，请重新登录！';
							this.REMOVE_INFO();
							this.$message.error(errorMsg);
							setTimeout(() => {
								this.$router.push('/login');
							}, 1500);
							return;
						} else {
							// 处理正常的数据响应
							const data = JSON.parse(dataStr);
							// 在这里处理校对结果数据
							console.log('校对数据:', data);
							if (data.message) {
								this.$refs.errorList.doneText = data.message || '正在纠错中...';
							}
							if (data && data.qualityGroup) {
								qualityGroup = data.qualityGroup;
							}
							if (eventValue === 'status_done') {
								if (qualityGroup) {
									this.$refs.errorList.getProofreadDetail(this.id);
								} else {
									await this.toNewText(qualityTypes);
								}
							}
						}
					} catch (parseError) {
						console.error('解析SSE数据出错:', parseError, '原始数据:', dataStr);
					}
				}
			}
			// 处理缓冲区中剩余的数据
			if (buffer.startsWith('data:')) {
				try {
					const dataStr = buffer.substring(5);
					const data = JSON.parse(dataStr);
					// 处理最后的数据
					console.log('校对数据:', data);
				} catch (parseError) {
					console.error('解析最后的SSE数据出错:', parseError, '原始数据:', buffer);
				}
			}
		},
		/**关闭所有弹窗*/
		cale() {
			this.showMore = false;
			this.aiShow = false;
			this.openPopup = false;
			this.currentUtil = '';
			this.$refs.coosEditor.openOutline = false;
		},
		// 内容变化
		changeTextHtml(text) {
			this.$refs.errorList.watchText(text);
		},
		/**根据数据初始化*/
		initData(data) {
			this.start();
			this.id = data.id;
			this.$nextTick(() => {
				this.content = data.content;
				if (this.$route.query.pathType === 'error') {
					setTimeout(() => {
						this.$refs.errorList.setData(data);
					}, 100);
				}
			});
			if (data.writeInfo) {
				this.outline = data.writeInfo.map(item => {
					return {
						...item,
						text: item.title
					};
				});
			}
			if (data.originInfo) {
				this.sourceArr = data.originInfo;
				setTimeout(() => {
					this.addEvent();
					this.$refs.coosEditor.aiEditor.focusStart();
				}, 0);
			}
			if (data.originMarkInfo) {
				this.originMarkInfo = data.originMarkInfo;
			}
		},
		/**替换*/
		replace(type, arr) {
			this.$refs.coosEditor.replace(type, arr);
		},
		/**关闭其他弹窗*/
		closeOther() {
			this.openPopup = false;
			this.aiShow = false;
			this.showMore = false;
		},
		setErrorContent(content, currentIndex) {
			this.$refs.coosEditor.isBottom = true;
			this.$refs.coosEditor.textIndex = currentIndex;
			if (content) {
				this.content = content;
			}
			// 只读模式
			this.$refs.coosEditor.aiEditor.setEditable(false);
			setTimeout(() => {
				this.addEvent();
			}, 1000);
		},
		/**ai智能对话*/
		aiQuery(obj, config) {
			this.$refs.aiContent.init(obj, config);
			this.handleUtils({ type: 'ai' }, true);
		},
		/**操作工具栏*/
		handleUtils(item, isOther = false) {
			switch (item.type) {
				case 'source':
					// if (!this.done) {
					// 	this.$message.warning('正在检索中，请稍后...');
					// 	return;
					// }
					// if (this.sourceArr.length === 0) {
					// 	this.$message.warning('没有匹配到相关信源！');
					// 	return;
					// }
					this.showMore = false;
					this.openError = false;
					this.aiShow = false;
					this.markContent = '';
					this.openPopup = !this.openPopup;
					this.selectSourceArr = [];
					this.$refs.coosEditor.openOutline = false;
					this.currentUtil = this.currentUtil === item.type ? '' : item.type;
					break;
				case 'ai':
					if (!this.done) {
						this.$message.warning('请耐心等待文章生成完毕！');
						return;
					}
					this.showMore = false;
					this.openPopup = false;
					this.openError = false;
					this.currentUtil = this.currentUtil === item.type && !isOther ? '' : item.type;
					this.aiShow = isOther || !this.aiShow;
					this.$refs.coosEditor.openOutline = false;
					break;
				case 'errorCorrection':
					this.showMore = false;
					this.openPopup = false;
					this.aiShow = false;
					this.openError = !this.openError;
					this.currentUtil = this.currentUtil === item.type ? '' : item.type;
					break;
			}
		},
		/**重置显示*/
		resetShow() {
			this.showMore = false;
			this.$refs.coosEditor.openOutline = true;
		},
		changeSaveSuccess(b) {
			this.saveSuccess = b;
		},
		/**保存大纲*/
		save(data) {
			this.$refs.coosEditor.save(data);
		},
		/**上一步*/
		pre() {
			this.$emit('changeCurrent', this.writeeType === 'quickly' || this.whetherToSkipOrNot ? 1 : 2);
		},
		/**打开设置*/
		openSetting(item) {
			this.openPopup = false;
			this.aiShow = false;
			this.$refs.coosEditor.openOutline = false;
			this.showMore = true;
			this.$refs.setting.open(item);
		},
		/**更新保存大纲*/
		async saveOutline(data) {
			if (!this.id) return;
			let res = await saveOutline(
				{
					saveType: 'outline',
					writeInfo: data
				},
				this.id
			);
			if (res.code === 200) {
				this.outline = res.result.writeInfo.map(item => {
					let obj = data.find(m => m.title === item.title);
					return {
						...obj,
						...item,
						text: item.title
					};
				});
				this.$emit('updateOutline', this.outline);
			} else {
				this.$message.error(res.message);
			}
		},
		/**
		 * @Descript 保存大纲
		 * @Param {String} id 文档id
		 * @Param {Array,Object} data 大纲信息 | 请求大纲的参数信息
		 * @Param {Boolean} isSaveOutline 是否需要保存大纲
		 * */
		async proOutline(id, data, isSaveOutline = true) {
			this.start();
			this.done = false;
			this.id = id;
			// 是否保存大纲
			if (isSaveOutline) {
				this.whetherToSkipOrNot = false;
				await this.saveOutline(data);
			} else {
				// 生成大纲的时候暂停
				this.reType = 'outline';
				this.reData = data;
				this.whetherToSkipOrNot = true;
				try {
					let res = await quicklyProOutlineRequest(data);
					if (res.code !== 200) {
						this.done = true;
						this.isError = true;
						this.$message.error('生成失败，请返回首页重新生成！');
						return;
					}
					this.id = res.result.id;
					this.outline = res.result.writeInfo.map(item => {
						return {
							...item,
							text: item.title
						};
					});
				} catch (err) {
					return;
				}
			}
			await this.generateFullText();
		},
		/**初始化变量*/
		start() {
			this.pendingText = '全文生成中...';
			this.isErr = false;
			this.id = '';
			this.getSourceStatus = false;
			this.isError = false;
			this.reType = '';
			this.reData = null;
			this.pause(false);
			// 回答中的状态
			this.done = true;
			this.$refs.coosEditor.reset();
			// 清空编辑器内容
			this.$refs.coosEditor.clear();
			// 清空源内容
			this.content = '';
			// 清空信源
			this.sourceArr = [];
			// 关闭信源
			this.openPopup = false;
			this.$refs.aiContent.allReset();
		},
		// 防止点击事件传入$event误以为是参数
		pro() {
			if (this.id) {
				this.generateFullText();
			} else if (this.reType === 'quickly') {
				this.generateFullText(this.reData);
			} else if (this.reType === 'outline') {
				this.proOutline('', this.reData, false);
			}
		},
		/**生成全文*/
		async generateFullText(data) {
			// 清空前临时保存
			let id = this.id;
			this.start();
			this.done = false;
			// 快速写文，清除id
			if (data) {
				this.reType = 'quickly';
				this.reData = data;
			} else {
				// 赋值回原来的值
				this.id = id;
			}
			if (!data && !this.id) {
				return;
			}
			this.controller = new AbortController();
			const { signal } = this.controller;
			let aiRes;
			try {
				aiRes = await generateFullText(data || { id: this.id }, signal);
				// 暂停时候特殊处理
				if (!aiRes) return;
			} catch (err) {
				this.content += '</hr><p style="color:red">生成异常，请重新生成...</p>';
				this.isErr = true;
				this.done = true;
				this.$message.error(err.message || err);
				return;
			}
			// // 获取AI答案
			const reader = aiRes.body.getReader(); // 流式读取器
			const decoder = new TextDecoder('utf-8');
			let readDone = false; // 读取完毕
			// 流式输出
			while (!readDone) {
				let { value, done } = await reader.read();
				readDone = done;
				let res;
				try {
					res = decoder.decode(value);
					// 缓冲区需要解析的流
					let buffer = '';
					if (/"code":50/.test(res)) {
						this.isErr = true;
						let r = JSON.parse(res.replace('data:', ''));
						res = r.message || '服务器异常';
					} else if (/"code":401/.test(res)) {
						this.isErr = true;
						res = '登录失效，请重新登录！';
						this.REMOVE_INFO();
						this.$message.error('登录失效，请重新登录！');
						setTimeout(() => {
							this.$router.push('/login');
						}, 1500);
					} else {
						// 如果下一次数据开始，就取缓冲区的数据进行处理，并重新赋值缓冲区
						if (/data:/.test(res)) {
							// 可能一次有多个data:
							let bufferArr = res.split('data:');
							// 最后一个data:之前的数据都加入缓冲区
							buffer = this.cacheItem + bufferArr.slice(0, -1).join('data:');
							// 取最后一个进行缓存
							this.cacheItem = 'data:' + bufferArr.slice(-1).join(',');
						}
						// 否则缓冲区进行累加
						else {
							this.cacheItem += res;
						}
					}
					if (this.isErr) {
						this.content = res;
					} else {
						// 累加数据
						this.clearBuffer(buffer, false);
					}
				} catch (err) {
					console.log('流式解析报错--------------', err);
				}
			}
			// 缓冲区还有东西，就要进行处理
			this.clearBuffer(this.cacheItem);
			// 回答完成
			this.done = true;
		},
		/**清空缓冲区*/
		clearBuffer(str, clear = true) {
			if (str) {
				str.split('data:').forEach(item => {
					if (item.indexOf('noAnswer') === -1) {
						let value = Base64.decode(item);
						if (/^id:/.test(value)) {
							this.id = value.replace('id:', '');
						} else if (/\[progress]/.test(value)) {
							this.pendingText = value.replace(/\[progress]/gi, '');
						} else {
							this.content += value;
						}
					}
				});
			}
			if (clear) {
				this.cacheItem = '';
			}
		},
		/**
		 * @Descripton获取来源
		 * @Param {Boolean} pending 是否在回答过程中
		 * */
		getSource(pending) {
			if (this.getSourceStatus || !this.id) return;
			this.getSourceStatus = true;
			// 如果是回答过程中获取信源就加上参数
			let params = {};
			if (pending) {
				params.isGrowing = true;
			}
			getSourceDetail(this.id, params).then(res => {
				this.getSourceStatus = false;
				// 生成中返回首页删除了数据，请求可能会报错
				if (!this.id) return;
				if (res.code === 200) {
					this.sourceArr = res.result.originInfo || [];
					this.originMarkInfo = res.result.originMarkInfo || {};
					this.addEvent();
				} else {
					this.$message.error(res.message);
				}
			});
		},
		/**添加监听事件*/
		/** 添加监听事件 */
		addEvent() {
			for (const dom of document.getElementsByTagName('mark')) {
				const parentElement = dom.parentNode;
				const isColorDifferent = parentElement.style.color !== 'rgb(51, 51, 51)';
				// 提取重复的 removeEventListener 和 addEventListener 逻辑
				const setupClickHandler = type => {
					dom.removeEventListener('click', e => this.markClick(e, type));
					dom.addEventListener('click', e => this.markClick(e, type));
				};
				if (parentElement.style.color && isColorDifferent) {
					setupClickHandler('error');
				} else {
					setupClickHandler('');
				}
			}
		},
		/**进入标记*/
		enterMark(e) {
			this.canCloseStatus = false;
			let hoverObj = this.sourceArr.find(item => {
				return (
					this.originMarkInfo &&
					this.originMarkInfo[e.target.innerText] &&
					this.originMarkInfo[e.target.innerText].split(',').includes(item.flag)
				);
			}) || { page_content: '未匹配到相关信源！' };
			this.hoverObj = deepClone(hoverObj);
			this.hoverObj.page_content = this.hoverObj.page_content.replaceAll(
				e.target.innerText,
				`<span style='color:var(--brand-6);font-weight: 600;font-size: 18px;'>${e.target.innerText}</span>`
			);
			this.hoverStyle = {
				top: e.clientY + 'px',
				left: e.clientX + 'px'
			};
			this.showTip = true;
		},
		/**移出标记*/
		leaveMark() {
			this.canCloseStatus = true;
			setTimeout(() => {
				if (this.canCloseStatus) {
					this.hoverObj = {};
					this.showTip = false;
				}
			}, 500);
		},
		changeSourceSelect(id) {
			this.selectSourceArr = [id];
		},
		/**点击标记*/
		markClick(e, type) {
			// if (this.sourceArr.length === 0) {
			// 	this.$message.warning('没有匹配到相关信源！');
			// 	return;
			// }
			console.log(e, type);
			if (type === 'error') {
				// 纠错
				this.openPopup = false;
				this.showMore = false;
				this.aiShow = false;
				this.openError = true;
				this.$refs.errorList.handleMark(e.target.innerText);
				this.currentUtil = 'errorCorrection';
				return;
			}
			this.markContent = e.target.innerText;
			this.openPopup = true;
			this.showMore = false;
			this.aiShow = false;
			this.$refs.coosEditor.openOutline = false;
			this.selectSourceArr =
				this.originMarkInfo && this.originMarkInfo[e.target.innerText]
					? this.originMarkInfo[e.target.innerText].split(',')
					: []; // 从映射中匹配来源，目前一对一
			this.currentUtil = 'source';
		},
		/**暂停*/
		pause(pending = true) {
			try {
				quicklyProOutlineCancel('quicklyProOutline');
				this.controller && this.controller.abort();
			} catch (e) {
				console.log(e);
			}
			this.controller = null;
			// 因为快速写文一开始就获取了信源  大纲写作每个大纲以及完成都会更新信源   所以这里暂停不需要再获取
			// if (this.id) {
			// 	this.getSource();
			// }
			// 缓冲区还有东西，就要进行处理
			this.clearBuffer(this.cacheItem);
			this.done = true;
			if (pending) {
				this.content +=
					'</hr><p style="color:red">回答中断，文章不完整，无法使用，请重新生成...</p>';
			}
		}
	}
};
</script>
<style scoped lang="scss">
.step3-page {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
}
.page-content {
	flex: 1;
	//padding: 29px 24px 24px;
	display: flex;
	overflow: hidden;
}
.step3 {
	height: 100%;
	width: 100%;
	overflow: hidden;
	display: flex;
}
.step3-showSource {
	background: #ffffff;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	//border-radius: 16px;
	//border: 1px solid #e3ebf2;
}
.step3-left {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;
	position: relative;
}
.step3-right {
	background: #ffffff;
	border-left: 1px solid #e3ebf2;
	width: 95px;
	padding: 36px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	//border-radius: 0 16px 16px 0;
	&-item {
		margin-bottom: 12px;
		display: flex;
		flex-direction: column;
		align-items: center;
		cursor: pointer;
		padding: 13px 3px;
		color: #15224c;
		&-icon {
			width: 32px;
			height: 32px;
			margin-bottom: 4px;
		}
		&-text {
			font-weight: 400;
			font-size: 14px;
			line-height: 16px;
		}
	}
}
.pro-buttons {
	display: flex;
	align-items: center;
	justify-content: center;
	//padding: 0 0 24px;
}
.edit {
	flex: 1;
	overflow: hidden;
}
.pro-right {
	margin-left: 16px;
	width: 400px;
	height: 100%;
	background: #ffffff;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	border-radius: 16px;
	border: 1px solid #d9e2ec;
}

.select-util {
	background: #f1f4f6;
	color: #2591f7;
	border-radius: 9px;
}
.pre-button {
	width: 82px;
	height: 36px;
	border-radius: 6px;
	background: #ffffff;
	border: 1px solid #bccadb;
	font-weight: 400;
	font-size: 14px;
	color: #2f446b;
	line-height: 22px;
}
.submit-button {
	width: 120px;
	height: 36px;
	background: #2591f7;
	box-shadow: 0px 4px 16px 0px rgba(37, 145, 247, 0.3);
	border-radius: 6px;
	border: none;
}
::v-deep .is-ghost {
	&:hover {
		border-color: #2591f7;
	}
}
.disabled-button {
	background: $holderTextColor;
	border-color: $holderTextColor;
	box-shadow: none;
	color: #ffffff !important;
	cursor: not-allowed;
}
.tip {
	max-width: 500px;
	max-height: 400px;
	position: fixed;
	padding: 24px;
	border-radius: 6px;
	background: #ffffff;
	box-shadow: 0px -1px 20px 0px rgba(184, 194, 215, 0.3);
	overflow: auto;
	@include noScrollBar;
}
.drag-icon {
	position: absolute;
	cursor: ew-resize;
	right: 4px;
	top: calc(50% - 9px);
	font-size: 16px;
}
@keyframes level {
	from {
		opacity: 1;
	}
	to {
		opacity: 0;
	}
}
.level-status {
	animation: level 0.5s forwards;
}
</style>
