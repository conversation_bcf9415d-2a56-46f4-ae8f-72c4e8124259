<template>
	<div class="source" :style="{ width: width + 'px' }">
		<div class="source-title">
			<div class="title-left">
				<img src="@/assets/images/write/error-correction-icon.png" class="logo-icon" alt="" />
				<span>文稿纠错</span>
			</div>
			<div class="title-right">
				<div class="title-dropdown">
					<el-popover width="400" trigger="click">
						<el-cascader-panel
							v-model="childrenActive"
							:options="errorListDown"
							:props="Props"
						></el-cascader-panel>
						<el-button slot="reference" type="text" class="button-text">
							<span class="send-text">
								纠错侧重点
								<i class="el-icon-arrow-down el-icon--right"></i>
							</span>
						</el-button>
					</el-popover>
					<!--					<el-dropdown>-->
					<!--						<span class="send-text">-->
					<!--							{{ dropdownItem.content ? dropdownItem.content : '纠错侧重点' }}-->
					<!--							<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
					<!--						</span>-->
					<!--						<el-dropdown-menu slot="dropdown">-->
					<!--							&lt;!&ndash;                    官方command指令报错    &ndash;&gt;-->
					<!--							<el-dropdown-item-->
					<!--								v-for="item of errorListDown"-->
					<!--								:key="item.id"-->
					<!--								@click.native="dropdownClick(item)"-->
					<!--							>-->
					<!--								<span>{{ item.content }}</span>-->
					<!--							</el-dropdown-item>-->
					<!--						</el-dropdown-menu>-->
					<!--					</el-dropdown>-->
				</div>
				<div class="anew" @click="toNewText">
					<i class="coos-iconfont icon-loop"></i>
					<span>重新纠错</span>
				</div>
			</div>
		</div>
		<div class="content">
			<div class="content-title">
				共识别到
				<span class="title-num">{{ newCount }}</span>
				条项目问题
			</div>
			<div class="content-tab">
				<div
					v-for="tab in errorTabs"
					:key="tab.code"
					class="tab-item"
					:class="{ active: tab.code === activeTab }"
					@click="handleTabClick(tab.code)"
				>
					<el-dropdown :hide-on-click="false">
						<span class="el-dropdown-link">
							{{ tab.name }}
							<span v-if="tab.count">·{{ tab.count }}</span>
						</span>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item v-for="event of tab.list" :key="event.errorType">
								<el-checkbox v-model="event.checked" @change="handleChecked(event)">
									{{ event.errorType }}
								</el-checkbox>
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
			</div>
			<div v-if="!done" class="pending">
				<div class="pending-left">
					<i class="el-icon-loading loading-icon"></i>
					<div>{{ doneText }}</div>
				</div>
				<!--			<el-button @click="pause">-->
				<!--				<i class="coos-iconfont icon-zanting pending-icon"></i>-->
				<!--				<span>停止</span>-->
				<!--			</el-button>-->
			</div>
			<div class="content-list">
				<div class="list">
					<div
						v-for="item in errorList"
						:key="item.id"
						:data-id="item.id"
						class="error-item"
						:class="{ 'is-checked': item.isChecked }"
					>
						<div v-if="item.dealStatus === 'replaced'" class="replaced">已替换</div>
						<!--						<div class="checkbox">-->
						<!--							<el-checkbox-->
						<!--								v-model="item.isChecked"-->
						<!--								:disabled="isCheckboxDisabled(item)"-->
						<!--								@change="handleCheckboxChange(item)"-->
						<!--							></el-checkbox>-->
						<!--						</div>-->
						<div
							class="item-box"
							:class="item.isChecked ? 'active-item-box' : ''"
							@click="activeItem(item)"
						>
							<div class="item-box-content">
								<div class="original-content">
									<span>原文内容：</span>
									<span class="original-content-text">{{ item.originalContent }}</span>
								</div>
								<div class="corrected-content">
									<span>修改建议：</span>
									<el-input
										v-if="editInput === item.id"
										v-model="updateValue"
										type="textarea"
										:rows="2"
										placeholder="请输入内容"
									></el-input>
									<div v-else>
										<span class="corrected-content-text">{{ item.correctedContent }}</span>
										<i
											class="coos-iconfont icon-bianji edit-icon-class"
											@click.stop="edit(item)"
										></i>
									</div>
									<div v-if="editInput === item.id" class="handle show-handle">
										<i
											class="coos-iconfont icon-selected handle-icon"
											@click.stop="updateText(item.id)"
										></i>
										<i
											class="coos-iconfont icon-close_circle handle-icon"
											@click.stop="editInput = ''"
										></i>
									</div>
								</div>
							</div>
							<div class="operation">
								<div :class="['error-type', item.errorTypeStyle]">
									<span>{{ item.errorType }}</span>
								</div>
								<div class="item-btm">
									<div
										v-if="item.dealStatus === 'untreated'"
										@click.stop="handleCommandItem('ignore', item)"
									>
										忽略
									</div>
									<el-divider direction="vertical"></el-divider>
									<div @click.stop="handleCommandItem('replace', item)">
										{{ item.dealStatus === 'replaced' ? '撤回' : '替换' }}
									</div>
									<el-divider direction="vertical"></el-divider>
									<el-dropdown
										@command="
											command => {
												handleCommandItem(command, item);
											}
										"
									>
										<span class="el-dropdown-link">
											<i class="coos-iconfont icon-more2 handle-icon"></i>
										</span>
										<el-dropdown-menu slot="dropdown">
											<el-dropdown-item command="copy">复制修改建议</el-dropdown-item>
											<el-dropdown-item command="addToLibrary">添加到素材库</el-dropdown-item>
										</el-dropdown-menu>
									</el-dropdown>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<el-checkbox v-model="selectAll">全选</el-checkbox>
			<div>
				<el-dropdown @command="handleCommand">
					<span class="el-dropdown-link">
						更多
						<i class="el-icon-arrow-down el-icon--right"></i>
					</span>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item command="ignoreAll">忽略所有</el-dropdown-item>
						<el-dropdown-item command="addToLibrary">添加到素材库</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
				<el-button @click="ignoreSelected">忽略</el-button>
				<el-button type="primary" @click="replaceAndAdopt(dealStatus)">
					{{ dealStatus === 'replaced' ? '一键撤回' : '采纳并替换原文' }}
				</el-button>
			</div>
		</div>
		<add-material ref="AddMaterial"></add-material>
	</div>
</template>

<script>
import AddMaterial from '@/views/draft-writing/components/add-material.vue';
import {
	getNewProofreadQualityType,
	getProofreadDetail,
	writeProofreadIgnore,
	writeProofreadUndo,
	writeProofreadUntreated
} from '@/api/modules/error-correction';
import { copyText } from '@/wile-fire/ui/util/clipboard';
import markMixins from '@/views/draft-writing/utils/mark-mixins';

export default {
	name: 'ErrorList',
	components: { AddMaterial },
	mixins: [markMixins],
	props: {
		width: {
			type: Number,
			default: () => {
				return 463;
			}
		},
		selectSourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		sourceArr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		markContent: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			errorTabs: [],
			activeTab: '', // 默认激活的标签
			currentOpenId: '',
			selectAll: false,
			editInput: '',
			done: false,
			updateValue: '',
			Props: {
				children: 'qualitys',
				value: 'code',
				label: 'name',
				multiple: true
			},
			id: '',
			content: '',
			dealStatus: '',
			childrenActive: [],
			textContent: '',
			oldContent: '',
			doneText: '正在纠错中...',
			contentIndex: 0,
			replacedList: [],
			baseProofreadResult: [], // 语言层面错误
			wbProofreadResult: [], //黑白名单错误
			logicProofreadResult: [], //逻辑错误
			errorListDown: [], // 纠错类型
			dropdownItem: {}, // 选中的纠错
			errorList: []
		};
	},
	computed: {
		newList() {
			if (this.activeTab == 0) {
				return this.errorList;
			} else {
				return this.errorList.filter(item => item.errorTypeKey === this.activeTab);
			}
		},
		newCount() {
			return (
				this.baseProofreadResult.length +
				this.wbProofreadResult.length +
				this.logicProofreadResult.length
			);
		}
	},
	watch: {
		selectAll(val) {
			if (val) {
				const checkedItems = this.errorList.filter(item => item.dealStatus !== 'replaced');
				// 全部都替换过了
				if (checkedItems.length === 0) {
					this.errorList.forEach(item => {
						item.isChecked = val;
					});
					this.dealStatus = 'replaced';
				} else {
					this.errorList.forEach(item => {
						if (item.dealStatus !== 'replaced') {
							item.isChecked = val;
						}
					});
					this.dealStatus = 'untreated';
				}
				this.selectErrorText(this.errorList);
				// this.$emit('selectErrorText', this.errorList);
			} else {
				this.selectErrorText([], this.errorList);
				// this.$emit('selectErrorText', [], this.errorList);
			}
		},

		errorList: {
			handler(newVal) {
				this.selectAll = newVal.every(item => item.isChecked);
				const checkedItems = this.errorList.filter(item => item.isChecked);
				if (checkedItems && checkedItems.length === 0) {
					this.dealStatus = '';
				}
				// const newCheckedItems = this.errorList.filter(item => !item.isChecked);
				// this.selectErrorText(checkedItems, newCheckedItems);
				// this.$emit('selectErrorText', checkedItems, newCheckedItems);
			},
			deep: true
		}
	},
	// 在组件创建时调用的方法，用于获取校对质量类型列表
	async created() {
		if (this.$route.query.pathType === 'error') {
			await this.getProofreadQualityTypeList();
		}
	},

	methods: {
		/**
		 * 处理文本标记点击事件，查找对应的错误项并高亮显示
		 * @param {string} text - 被点击的文本内容
		 */
		handleMark(text) {
			const targetItem = this.errorList.find(
				item => item.originalContent === text || item.correctedContent === text
			);
			if (targetItem) {
				this.errorList.forEach(item => {
					item.isChecked = item.id === targetItem.id;
				});
				this.$nextTick(() => {
					this.scrollToItem(targetItem);
				});
			}
		},
		/**
		 * 滚动到指定的错误项元素
		 * @param {Object} item - 错误项对象
		 */
		scrollToItem(item) {
			const itemElement = document.querySelector(`.error-item[data-id="${item.id}"]`);
			if (itemElement) {
				itemElement.scrollIntoView({ behavior: 'smooth' });
			}
		},
		/**
		 * 判断复选框是否禁用
		 * @param {Object} item - 错误项对象
		 * @returns {boolean} - 是否禁用
		 */
		isCheckboxDisabled(item) {
			if (!this.dealStatus) return false;
			return this.dealStatus !== item.dealStatus;
		},
		/**
		 * 选择/取消激活错误项
		 * @param {Object} item - 错误项对象
		 */
		activeItem(item) {
			if (this.dealStatus === '' || this.dealStatus === item.dealStatus) {
				item.isChecked = !item.isChecked;
			}
			// 更新 dealStatus：始终取当前选中项的值，若无选中项则清空
			let checkedItems = this.errorList.filter(item => item.isChecked);
			if (checkedItems.length > 0) {
				this.dealStatus = checkedItems[0].dealStatus; // 或根据业务需求取最后一个
			} else {
				this.dealStatus = '';
			}
			// 更新 selectAll 状态
			this.selectAll = this.errorList.every(item => item.isChecked);
			// 提取重复逻辑为函数
			const updateContentText = items => {
				return items.map(item => {
					if (item.dealStatus === 'replaced') {
						item.originalContentText = item.correctedContent;
						item.correctedContentText = item.originalContent;
					} else {
						item.originalContentText = item.originalContent;
						item.correctedContentText = item.correctedContent;
					}
					return item;
				});
			};
			// 更新 contentIndex：始终取第一个选中项的位置
			if (checkedItems.length > 0) {
				const firstCheckedItem = checkedItems[0];
				const index = this.content.indexOf(firstCheckedItem.originalContentText);
				this.contentIndex = index !== -1 ? index : -1; // 明确处理未找到的情况
			} else {
				this.contentIndex = -1;
			}
			// 更新所有项的 contentText
			const newCheckedItems = this.errorList.filter(item => !item.isChecked);
			const updatedCheckedItems = updateContentText(checkedItems);
			const updatedNewCheckedItems = updateContentText(newCheckedItems);

			this.selectErrorText(updatedCheckedItems, updatedNewCheckedItems);
		},

		/**
		 * 获取新的校对质量类型列表
		 */
		getProofreadQualityTypeList() {
			getNewProofreadQualityType()
				.then(res => {
					if (res.code === 200) {
						this.errorTabs = res.result;
						this.activeTab = this.errorTabs[0].code;

						const arr = res.result;
						this.errorListDown = [];
						this.childrenActive = [];

						arr.forEach(item => {
							if (item.code === 'base') {
								this.errorListDown.push(item);
							} else {
								item.qualitys.forEach(quality => {
									if (quality.code === 'wrong:black') {
										const obj = { ...item, qualitys: [quality] };
										this.errorListDown.push(obj);
									}
								});
							}
						});
						this.errorListDown.forEach(item => {
							item.qualitys.forEach(event => {
								this.childrenActive.push([item.code, event.code]);
							});
						});
					} else {
						this.$message.error(res.message || '获取纠错类型失败');
					}
				})
				.catch(error => {
					this.$message.error('请求出错');
					console.error('获取纠错类型接口异常:', error);
				});
		},
		/**
		 * 设置数据
		 * @param {Object} data - 数据对象
		 */
		setData(data) {
			// 数据校验
			const {
				id,
				content,
				baseProofreadResult = [],
				wbProofreadResult = [],
				logicProofreadResult = []
			} = data;

			this.id = id;
			this.oldContent = content;
			this.content = content;

			// 类型检查，防止访问 length 报错
			const isEmptyArray = arr => arr.length === 0;

			try {
				if (
					isEmptyArray(baseProofreadResult) &&
					isEmptyArray(wbProofreadResult) &&
					isEmptyArray(logicProofreadResult)
				) {
					this.done = true;
					this.toNewText();
					return;
				}
			} catch (e) {
				this.done = true;
				this.toNewText();
				return;
			}

			this.done = true;

			// 封装过滤逻辑
			const filterIgnored = arr =>
				Array.isArray(arr) ? arr.filter(item => item.dealStatus !== 'ignored') : [];
			this.baseProofreadResult = filterIgnored(baseProofreadResult);
			this.logicProofreadResult = filterIgnored(logicProofreadResult);
			this.wbProofreadResult = filterIgnored(wbProofreadResult);
			// 合并错误类型函数提取到外部更好，此处保留原逻辑
			const mergeByErrorType = tabArr => {
				return tabArr.reduce((acc, item) => {
					const existingItem = acc.find(i => i.errorType === item.errorType);
					if (existingItem) {
						existingItem.count++;
					} else {
						acc.push({ ...item, checked: true, count: 1 });
					}
					return acc;
				}, []);
			};
			// 使用映射优化 tab 更新逻辑
			const resultMap = {
				base: {
					list: mergeByErrorType(this.baseProofreadResult),
					count: this.baseProofreadResult.length
				},
				logic: {
					list: mergeByErrorType(this.logicProofreadResult),
					count: this.logicProofreadResult.length
				},
				// 黑白名单展示0
				wb: {
					list: mergeByErrorType(this.wbProofreadResult),
					count: this.wbProofreadResult.length ? this.wbProofreadResult.length : '0'
				}
			};
			this.errorTabs = this.errorTabs.map(item => {
				const result = resultMap[item.code];
				if (result) {
					return { ...item, ...result };
				}
				return { ...item };
			});

			// 根据 activeTab 设置错误列表
			const tabMap = {
				base: this.baseProofreadResult,
				logic: this.logicProofreadResult,
				wb: this.wbProofreadResult
			};

			const targetList = tabMap[this.activeTab];
			if (targetList) {
				this.setErrorList(targetList);
			}
		},

		/**
		 * 处理选中状态变化
		 */
		handleChecked() {
			const findMatchingBaseProofreadResults = errorType => {
				if (this.activeTab === 'base') {
					return this.baseProofreadResult.filter(event => event.errorType === errorType);
				}
				if (this.activeTab === 'logic') {
					return this.logicProofreadResult.filter(event => event.errorType === errorType);
				}
				if (this.activeTab === 'wb') {
					return this.wbProofreadResult.filter(event => event.errorType === errorType);
				}
			};

			let selectedResults = [];
			try {
				selectedResults = this.errorTabs
					.flatMap(item => (item.code === this.activeTab ? item.list : []))
					.filter(element => element.checked)
					.flatMap(element => findMatchingBaseProofreadResults(element.errorType));
			} catch (e) {
				selectedResults = [];
			}
			// 设置错误列表 红色下划线
			this.setErrorList(selectedResults);
		},
		/**
		 * 下拉菜单点击处理
		 * @param {Object} item - 菜单项对象
		 */
		dropdownClick(item) {
			this.dropdownItem = item;
		},

		/**
		 * 标签页点击处理
		 * @param {string} key - 标签页key
		 */
		handleTabClick(key) {
			if (!this.done) {
				this.$message.warning('正在校对中！');
				return;
			}
			this.activeTab = key;
			if (key === 'logic' && this.logicProofreadResult.length === 0) {
				const qualityTypes = this.errorTabs
					.find(item => item.code === this.activeTab)
					.qualitys.map(event => event.code);
				this.done = false;
				this.$emit('toNewText', qualityTypes, false);
			}
			this.handleChecked();
		},

		/**
		 * 编辑操作
		 * @param {Object} item - 错误项对象
		 */
		edit(item) {
			this.editInput = item.id;
			this.updateValue = item.correctedContent;
		},

		/**
		 * 更新文本内容
		 * @param {number} id - 错误项ID
		 */
		updateText(id) {
			const item = this.errorList.find(item => item.id === id);
			if (item && this.updateValue.trim() !== '') {
				item.correctedContent = this.updateValue.trim();
				this.editInput = '';
				this.updateValue = '';
			} else {
				this.$message.warning('修改内容不能为空');
			}
		},

		/**
		 * 保存输入内容
		 * @param {Object} row - 行数据
		 */
		saveInput(row) {},

		/**
		 * 处理命令项
		 * @param {string} command - 命令类型
		 * @param {Object} row - 行数据
		 */
		handleCommandItem(command, row) {
			const checkedItems = this.errorList.filter(item => item.id === row.id);
			let stack = [];
			let dealStatus = '';
			switch (command) {
				case 'ignore':
					checkedItems.forEach(item => {
						item.dealStatus = 'ignored';
					});
					this.errorList.forEach(item => {
						if (item.isChecked) {
							item.isChecked = false;
						}
					});
					//替换、 撤回还原文本
					stack = checkedItems.map(item => ({
						...item,
						oldText: item.originalContentText,
						newText: item.correctedContentText
					}));
					this.replacedList = stack;
					this.ignoreSelectedFun(checkedItems);
					this.dealStatus = '';
					break;
				case 'replace':
					checkedItems.forEach(item => {
						if (item.dealStatus === 'replaced') {
							item.originalContentText = item.correctedContent;
							item.correctedContentText = item.originalContent;
						} else {
							item.originalContentText = item.originalContent;
							item.correctedContentText = item.correctedContent;
						}
						item.dealStatus = item.dealStatus === 'replaced' ? 'untreated' : 'replaced';
						dealStatus = item.dealStatus === 'replaced' ? 'untreated' : 'replaced';
					});
					this.errorList.forEach(item => {
						if (item.isChecked) {
							item.isChecked = false;
						}
					});
					//替换、 撤回还原文本
					stack = checkedItems.map(item => ({
						...item,
						oldText: item.originalContentText,
						newText: item.correctedContentText
					}));
					this.replacedList = stack;
					this.replaceAndAdoptFun(checkedItems, dealStatus);
					this.dealStatus = '';
					break;
				case 'copy':
					copyText(row.correctedContentText);
					this.$message.success('复制成功！');
					break;
				case 'addToLibrary':
					this.$refs.AddMaterial.open(checkedItems, this.id);
					break;
				default:
					console.warn('未知命令:', command);
			}
		},

		/**
		 * 处理命令
		 * @param {string} command - 命令类型
		 */
		handleCommand(command) {
			if (command === 'ignoreAll') {
				this.errorList.forEach(item => {
					item.dealStatus = 'ignored';
				});
				this.ignoreSelectedFun(this.errorList);
				this.errorList.forEach(item => {
					if (item.isChecked) {
						item.isChecked = false;
					}
				});
				this.replacedList = this.errorList;
				this.dealStatus = '';
			} else if (command === 'addToLibrary') {
				const checkedItems = this.errorList.filter(item => item.isChecked);
				this.$refs.AddMaterial.open(checkedItems, this.id);
			}
		},

		/**
		 * 替换并采用处理
		 * @param {string} dealStatus - 最后处理结果
		 */
		replaceAndAdopt(dealStatus) {
			const checkedItems = this.errorList.filter(item => item.isChecked);
			checkedItems.forEach(item => {
				if (dealStatus === 'replaced') {
					// 撤回
					item.originalContentText = item.correctedContent;
					item.correctedContentText = item.originalContent;
				} else {
					item.originalContentText = item.originalContent;
					item.correctedContentText = item.correctedContent;
				}
				item.dealStatus = dealStatus === 'replaced' ? 'untreated' : 'replaced';
			});
			this.errorList.forEach(item => {
				if (item.isChecked) {
					item.isChecked = false;
				}
			});
			this.replacedList = checkedItems.map(item => ({
				...item,
				oldText: item.originalContentText,
				newText: item.correctedContentText
			}));
			let newDealStatus = dealStatus === 'replaced' ? 'untreated' : 'replaced';
			// this.updateDealResult(stack);
			this.replaceAndAdoptFun(checkedItems, newDealStatus);
			this.dealStatus = '';
		},

		/**
		 * 监听文本变化
		 * @param {string} text - 文本内容
		 */
		watchText(text) {
			this.textContent = text;
			if (text && this.replacedList && this.replacedList.length) {
				this.updateDealResult(this.replacedList);
			}
		},

		/**
		 * 转换为新文本
		 */
		toNewText() {
			if (!this.done) {
				this.$message.warning('正在校对中！');
				return;
			}
			this.activeTab = 'base';
			const qualityTypes = this.childrenActive.map(item => item[1]);
			this.done = false;
			this.errorList = [];
			this.$emit('toNewText', qualityTypes, true);
		},
		getProofreadDetail(id) {
			getProofreadDetail(id).then(res => {
				if (res.code === 200) {
					let data = {
						...res.result,
						content: res?.result?.newText || res?.result?.text || ''
					};
					this.setData(data);
				}
			});
		},
		/**
		 * 更新处理结果
		 * @param {Array} stack - 栈数据
		 */
		async updateDealResult(stack) {
			let obj = {
				id: this.$route.query.id,
				stack: stack,
				newText: this.textContent
			};
			let res = null;
			if (stack[0].dealStatus === 'replaced') {
				res = await writeProofreadUndo(obj);
			}
			if (stack[0].dealStatus === 'untreated') {
				res = await writeProofreadUntreated(obj);
			}
			if (stack[0].dealStatus === 'ignored') {
				res = await writeProofreadIgnore(obj);
			}
			if (res.code === 200) {
				this.replacedList = [];
				this.$message.success('处理成功');
				this.getProofreadDetail(this.id);
			} else {
				this.replacedList = [];
				this.$message.error(res.message);
			}
		}
	}
};
</script>

<style scoped lang="scss">
.source {
	min-width: 350px;
	height: 100%;
	display: flex;
	background: #ffffff;
	flex-shrink: 0;
	margin-left: 16px;
	flex-direction: column;

	.logo-icon {
		height: 32px;
		width: 32px;
		margin-right: 4px;
	}

	&-title {
		height: 56px;
		padding: 0 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-weight: 600;
		font-size: 20px;
		color: #15224c;
		line-height: 23px;
		border-bottom: 1px solid #e3ebf2;
	}
	.title-left {
		display: flex;
		align-items: center;
	}
	.title-right {
		display: flex;
		align-items: center;
	}

	.content {
		flex: 1;
		overflow: auto;
		padding: 26px 0px;

		&-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #15224c;
			line-height: 19px;
			padding: 0 20px;

			.title-num {
				color: #e34d59;
			}
		}

		.content-tab {
			display: flex;
			padding: 0 20px;
			flex-wrap: wrap;
			white-space: nowrap; /* 防止子元素换行 */
			margin: 16px 0;
			.tab-item {
				padding: 8px 16px;
				margin-right: 8px;
				margin-bottom: 12px;
				border-radius: 4px;
				background-color: #f5f6fa;
				color: #737a94;
				cursor: pointer;
				flex-shrink: 0; /* 防止子元素被压缩 */
				white-space: nowrap; /* 确保文本不换行 */

				&.active {
					background-color: rgba(37, 145, 247, 0.1);
					color: #2591f7;
				}
			}
		}
		&-list {
			height: calc(100% - 146px);
			overflow-y: auto;
		}
		.list {
			padding: 0 20px;
			margin-top: 16px;
			cursor: pointer;
			.error-item {
				display: flex;
				position: relative;
				margin-bottom: 16px;
				.item-box {
					flex: 1;
					padding: 26px 16px 16px;
					background: #ffffff;
					border-radius: 9px 9px 9px 9px;
					border: 1px solid #dfebf6;
					&-content {
						flex: 1;
						border-bottom: 1px solid #dfebf6;
						.original-content,
						.corrected-content {
							display: flex;
							margin-bottom: 8px;
							span {
								min-width: 88px;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 16px;
								color: #2f446b;
							}
							.original-content-text {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #e34d59;
								line-height: 20px;
							}
							.corrected-content-text {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								color: #2591f7;
								line-height: 20px;
							}
						}
					}
				}
				&.is-checked {
					border-color: #2591f7;
				}
				.checkbox {
					margin-right: 16px;
				}
				.operation {
					margin-top: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.item-btm {
						display: flex;
						align-items: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						color: #2f446b;
						line-height: 20px;
					}
					.error-type {
						padding: 2px 8px;
						border-radius: 3px 3px 3px 3px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 12px;
						line-height: 20px;
					}
					select {
						padding: 4px 8px;
					}
				}
			}
			::v-deep .el-checkbox__inner {
				border-radius: 50%;
				&::after {
					left: 4px;
				}
			}
		}
	}
}

.footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 20px;
	border-top: 1px solid #e3ebf2;

	.el-checkbox {
		margin-right: 16px;
	}

	.el-dropdown-link {
		margin-right: 16px;
		cursor: pointer;
	}

	.el-button {
		margin-left: 16px;
	}
}
.active-item-box {
	border: 1px solid var(--brand-6) !important;
}
.replaced {
	position: absolute;
	width: 49px;
	right: 0;
	border-top-right-radius: 9px;
	border-bottom-left-radius: 9px;
	height: 20px;
	background: radial-gradient(10% 10px at center left, transparent 100%, #ff850b 0) center left;
	//background: radial-gradient(50% 10px at center bottom, transparent 100%, #ff850b 0) center bottom;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 10px;
	color: #ffffff;
}
.title-dropdown {
	padding: 5px 8px;
	display: flex;
	align-items: center;
	background: #f1f4f6;
	border-radius: 6px 6px 6px 6px;
	cursor: pointer;
	.send-text {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2f446b;
	}
}
.anew {
	margin-left: 8px;
	cursor: pointer;
	background: #ffffff;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #2591f7;
	padding: 4px 15px;
	display: flex;
	align-items: center;
	i {
		font-size: 14px;
		display: flex;
		align-items: center;
		margin-right: 4px;
		color: #2591f7;
		border-radius: 0px 0px 0px 0px;
	}
	span {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #2591f7;
	}
}
.pending {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #ffffff;
	padding: 20px;
	z-index: 670;
}
.pending-left {
	display: flex;
	align-items: center;
	font-size: 14px;
}
.pending-icon {
	color: #ff7a7b;
	font-size: 16px;
	margin-right: 4px;
}
.loading-icon {
	font-size: 16px;
	margin-right: 12px;
	color: var(--brand-6);
}
.handle {
	margin-left: 10px;
	&-icon {
		font-size: 18px;
		margin-left: 4px;
		color: $subTextColor;
	}
}
.edit-icon-class {
	font-size: 14px;
	margin-left: 4px;
}

/* 常识错误 */
.common {
	color: #e34d59;
	background-color: rgba(227, 77, 89, 0.1);
}

/* 搭配不当 */
.spelling {
	color: #ffc012;
	background-color: rgba(255, 192, 18, 0.1);
}

/* 疑似错误 */
.semantic {
	color: #ed7b2f;
	background-color: rgba(237, 123, 47, 0.1);
}
</style>
