<template>
	<div class="editor-content">
		<div class="editor-top">
			<div v-show="openOutline" class="outline">
				<div class="outline-title">
					<span>
						大纲
						<i v-if="!done" class="el-icon-loading loading-icon" style="margin-left: 8px"></i>
					</span>
					<i class="coos-iconfont close-outline-icon icon-guanbi1" @click="openOutline = false"></i>
				</div>
				<div class="outline-content">
					<div
						v-for="(item, index) of computedOutline"
						:key="index"
						class="outline-content-item"
						:style="{ paddingLeft: (item.level - 1) * 24 + 'px' }"
						@click="toPos(item)"
					>
						<div class="outline-content-item-left">
							<div class="outline-content-item-left-text">
								<div class="label-text" :class="{ 'outline-docTitle': item.level === 0 }">
									{{ item.text }}
								</div>
								<div
									v-if="item.descType === 'upload'"
									class="tag-content"
									@click.stop="openSetting(item)"
								>
									<i class="coos-iconfont icon-fujian1 tag-icon"></i>
									<span>上传文档</span>
								</div>
								<div
									v-if="item.descType === 'input'"
									class="tag-content"
									@click.stop="openSetting(item)"
								>
									<i class="coos-iconfont icon-bianji tag-icon"></i>
									<span>参考内容</span>
								</div>
								<div
									v-if="item.descType === 'select'"
									class="tag-content"
									@click.stop="openSetting(item)"
								>
									<i class="coos-iconfont icon-biaoqian1 tag-icon"></i>
									<span>知识库</span>
								</div>
							</div>
						</div>
						<i
							v-if="done && writeeType === 'deepWrite' && index > 0 && !item.descType"
							class="coos-iconfont setting icon-edit"
							@click.stop="openSetting(item)"
						></i>
					</div>
				</div>
			</div>
			<div ref="divRef" class="ai-helper-center" @contextmenu.prevent>
				<div class="aie-container">
					<div class="aie-header-panel">
						<div class="aie-container-header"></div>
					</div>
					<div class="aie-main">
						<div class="aie-main-content">
							<div class="aie-container-panel" :style="{ paddingBottom: done ? '' : '120px' }">
								<!--@mousedown="mousedown"-->
								<div
									ref="scrollContent"
									class="aie-container-main"
									@click="focus"
									@mouseup="mouseup"
									@scroll="scroll"
								></div>
								<!--							<i class="coos-iconfont icon-guaijiao-lt doc-icon top-left"></i>-->
								<!--							<i class="coos-iconfont icon-guaijiao-lt doc-icon top-right"></i>-->
								<!--							<i class="coos-iconfont icon-guaijiao-lt doc-icon bottom-right"></i>-->
								<!--							<i class="coos-iconfont icon-guaijiao-lt doc-icon bottom-left"></i>-->
								<div v-if="!done" class="pending">
									<!--									<div class="process" :style="{ width: process }"></div>-->
									<div class="pending-left">
										<i class="el-icon-loading loading-icon"></i>
										<div>{{ pendingText }}</div>
									</div>
									<el-button @click="pause">
										<i class="coos-iconfont icon-zanting pending-icon"></i>
										<span>停止</span>
									</el-button>
								</div>
							</div>
						</div>
					</div>
					<div class="aie-container-footer"></div>
				</div>
			</div>
		</div>
		<div class="footer">
			<div class="number">{{ total }}个字</div>
			<div class="copy" :class="{ disable: !done || !total }" @click="copy">
				<i class="coos-iconfont footer-icon icon-fuzhi"></i>
				<span>复制文本</span>
			</div>
		</div>
	</div>
</template>
<script>
import { AiEditor } from 'aieditor';
import 'aieditor/dist/style.css';
import { copyText } from '@/wile-fire/ui/util/clipboard';
import { saveOutline } from '@/api/modules/coos-write';
import { debounce, isEmpty } from '@/utils';
import { downloadFile } from '@/utils/down-load';
export default {
	name: 'EditorIndex',
	props: {
		current: {
			type: Number,
			default: () => {
				return 1;
			}
		},
		// 是否回答完成
		done: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// 内容
		content: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 初始化大纲
		initOutline: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 生成全文的id
		id: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 写作类型
		writeeType: {
			type: String,
			default: () => {
				return '';
			}
		},
		docTitle: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 生成进度
		pendingText: {
			type: String,
			default: () => {
				return '全文生成中...';
			}
		}
	},
	data() {
		return {
			tempPause: false, // 临时暂停
			isBottom: true, // 处于底部
			brushStyle: null, // 格式刷样式
			saveTitleStatus: false, // 存储标题的状态
			doneFirstOutline: false, // 第一次加载完成生成大纲
			saveLoading: false,
			T: null,
			pauseStatus: false,
			disableSave: true, // 禁止自动保存
			tempOutline: [], // 临时大纲
			outline: [],
			textIndex: 0,
			contentHtml: '',
			openOutline: false,
			total: 0
		};
	},
	computed: {
		computedOutline() {
			return this.done ? this.outline : this.tempOutline;
		},
		process() {
			return (this.pendingText.replace(/(全文生成中...|\(|\/100\))/gi, '') || 0) + '%';
		}
	},
	watch: {
		content(newVal, oldVal) {
			// 插入的时候有些标签源码渲染
			// this.aiEditor.focus().insert(newVal.slice(oldVal.length)).focusEnd();
			if (this.isBottom && !this.tempPause) {
				if (this.textIndex) {
					this.aiEditor.focus().clear().setContent(newVal).focusPos(this.textIndex);
					return;
				}
				this.aiEditor.focus().clear().setContent(newVal).focusEnd();
			}
		},
		// 如果回答完成，重新生成校验大纲
		done(newVal) {
			if (newVal) {
				// 防止滚动的时候没有聚焦在末尾
				if (!this.isBottom && !this.pauseStatus) {
					this.$message.success('生成完成！');
					this.aiEditor.focus().clear().setContent(this.content).focusEnd();
				}
				this.doneFirstOutline = true;
				// 第一次直接是保存状态
				this.$emit('changeSaveSuccess', true);
				// 生成最终的大纲
				this.finallyOutline();
				// 存储标题
				this.saveTitle();
				// 回答完成之后内容变化允许自动保存
				this.$nextTick(() => {
					this.disableSave = false;
				});
				// 只有第一次回答完毕才更新信源
				this.updateSourceInfo();
			}
		},
		current(newVal, oldVal) {
			if (oldVal === 3) {
				this.saveContentNow();
			}
		}
	},
	mounted() {
		let exportBtn = {};
		if (this.$route.query.pathType === 'error') {
			exportBtn = {
				title: '导出',
				icon: "<div class='export-class' style='font-size: 15px;color:#15224C'>导出</div>",
				onClick: () => {
					this.errorExport();
				}
			};
		} else {
			exportBtn = {
				icon: "<div class='export-class' style='font-size:14px;font-weight: 600;color:#15224c'>导出</div>",
				toolbarKeys: [
					{
						icon: '<svg t="1745559948042" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8824" width="200" height="200"><path d="M957.364628 542.665224c44.497864 0 66.751916 58.564105 66.751916 175.687193v129.96039c0 117.123089-22.248932 175.687193-66.751916 175.687193H66.751916C22.254053 1024 0 965.435895 0 848.312807v-129.96039c0-29.202683 1.382557-54.764632 4.147672-76.685847L77.197905 896.103205h46.085245l51.615473-197.98221h1.474728L227.620143 896.103205h45.716563l75.948483-263.238916h-48.666019l-49.403382 200.194302h-1.474727L197.756905 632.864289h-44.610517l-51.984156 200.194302h-1.474728L50.284122 632.864289H5.366371c9.037829-60.136123 29.494556-90.199065 61.385545-90.199065h890.612712z m-472.588821 157.667863c-29.125875 0-52.352838 9.217049-69.680889 28.38851-17.696734 18.80278-26.176419 42.767107-26.176419 72.261664 0 29.125875 8.479685 53.090202 25.807737 71.524299 17.696734 19.171462 40.923697 28.757193 70.049571 28.757193 28.757193 0 52.352838-9.585731 70.049572-28.757193 16.95937-18.434098 25.807737-42.398425 25.807737-71.524299 0-29.494556-8.848367-53.458884-26.176419-72.261664-17.696734-19.171462-40.923697-28.388511-69.68089-28.38851z m494.402503-72.630346h-42.029742v103.968312c-15.853324-21.014871-34.656104-31.337966-56.777022-31.337966-27.282465 0-49.0347 9.954413-64.519342 29.863238-14.747278 18.434098-21.752235 41.661061-21.752235 69.68089 0 29.125875 7.373639 53.090202 22.120917 71.524299 15.853324 19.908826 37.974241 29.863238 66.362752 29.863239 25.070373 0 44.241835-8.479685 57.514385-25.439055V896.103205h39.080287v-268.400464z m-248.675979 72.630346c-11.797823 0-21.752235 3.318138-30.23192 10.323094-7.004957 5.161547-12.903868 12.535187-18.065416 22.120918v-27.282465h-41.661061V896.103205h41.661061v-101.018856c0-16.222006 4.792865-29.125875 15.11596-39.080287 8.848367-8.848367 18.80278-12.903868 29.863239-12.903868 8.848367 0 17.696734 1.106046 27.282464 4.055501v-42.029743c-6.636275-3.318138-14.747278-4.792865-23.964327-4.792865z m159.823628 33.91874c13.27255 0 24.701691 5.530229 33.550058 16.590688 9.585731 11.797823 14.747278 28.019829 14.747278 48.297336v2.949456c0 18.434098-4.055502 33.91874-11.797822 45.716562-8.848367 12.903868-21.752235 19.540144-37.974242 19.540144-18.80278 0-32.444012-6.636275-40.923697-19.908826-7.373639-11.060459-10.691777-26.913783-10.691777-47.559972s3.68682-36.130832 11.429141-46.822609c8.848367-12.535187 22.489599-18.80278 41.661061-18.802779z m-405.550152 0.368682c17.328052 0 30.969284 6.636275 40.555016 20.646189 8.111003 11.797823 12.535187 27.282465 12.535186 45.716563 0 18.065416-4.424183 33.181376-12.535186 45.34788-9.585731 13.641232-23.226963 20.64619-40.555016 20.64619-17.696734 0-30.969284-7.004957-40.555015-20.64619-8.111003-11.797823-12.166505-26.913783-12.166504-45.34788 0-18.434098 4.055502-33.91874 12.166504-45.716563 9.585731-14.009914 22.858281-20.64619 40.555015-20.646189z" p-id="8825"></path><path d="M901.038218 249.143181L600.203983 3.723893a16.457553 16.457553 0 0 0-26.883059 12.617115v91.438246a16.641894 16.641894 0 0 1-14.993066 16.278333c-389.532969 38.40437-438.910748 358.440791-441.286699 509.861542a16.462673 16.462673 0 0 0 31.271399 7.317313c73.152645-150.693629 227.865931-230.426223 407.81857-237.743536a16.641894 16.641894 0 0 1 17.189796 16.641894v88.150832a16.457553 16.457553 0 0 0 26.883059 12.801457l300.834235-246.888896a16.641894 16.641894 0 0 0 0-25.055012z" p-id="8826"></path><span>导出Word</span></svg>',
						onClick: (event, editor) => {
							this.export('word');
						},
						tip: '导出Word文档'
					},
					{
						icon: '<svg t="1745559895167" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7834" width="200" height="200"><path d="M957.364628 542.665224c44.497864 0 66.751916 58.564105 66.751916 175.687193v129.96039c0 117.123089-22.248932 175.687193-66.751916 175.687193H66.751916C22.254053 1024 0 965.435895 0 848.312807v-129.96039c0-117.123089 22.248932-175.687193 66.751916-175.687193h890.612712zM251.676641 653.34662H142.546782V916.585536h43.135789v-101.018856h65.256706c64.15066 0 96.225991-27.282465 96.22599-81.478712 0-53.827566-32.07533-80.741348-95.488626-80.741348z m251.625435 0H407.076085V916.585536H503.302076c42.767107 0 74.842437-11.797823 96.963354-35.393468 21.014871-22.489599 31.706648-54.564929 31.706648-96.22599 0-42.029743-10.691777-74.105073-31.706648-96.225991-22.120917-23.595645-54.196248-35.393468-96.963354-35.393467z m371.815752 0h-179.548112V916.585536h43.135789v-116.503498h128.670002v-36.868195h-128.670002v-72.999028h136.412323v-36.868195z m-379.926755 36.868195c32.812694 0 56.777021 7.373639 71.892981 22.4896 14.747278 14.747278 22.120917 39.080287 22.120918 72.261663 0 32.444012-7.373639 56.408339-22.120918 71.892981-15.11596 15.11596-39.080287 22.858281-71.892981 22.858282h-44.979199v-189.502526h44.979199z m-246.83257 0c18.80278 0 32.812694 3.318138 41.661061 10.323095 8.848367 6.636275 13.641232 17.696734 13.641233 33.550058 0 15.853324-4.424183 27.282465-13.272551 34.287422-8.848367 6.636275-22.858281 10.323095-42.029743 10.323095h-62.675932v-88.48367h62.675932z" p-id="7835"></path><path d="M600.203983 3.723893a16.457553 16.457553 0 0 0-26.883059 12.617115v91.438246a16.641894 16.641894 0 0 1-14.993066 16.278333c-389.532969 38.40437-438.910748 358.440791-441.291819 509.861542a16.462673 16.462673 0 0 0 31.276519 7.317313c73.152645-150.693629 227.865931-230.426223 407.81857-237.743536a16.641894 16.641894 0 0 1 17.189796 16.641894v88.150832a16.457553 16.457553 0 0 0 26.883059 12.801457l300.834235-246.888896a16.641894 16.641894 0 0 0 0-25.055012L600.203983 3.723893z" p-id="7836"></path></svg><span>导出PDF</span>',
						onClick: (event, editor) => {
							this.export('pdf');
						},
						tip: '导出PDF文档'
					},
					'printer'
				]
			};
		}
		this.aiEditor = new AiEditor({
			element: this.$refs.divRef,
			placeholder: '',
			content: '',
			toolbarSize: 'small',
			draggable: false,
			onChange: aiEditor => {
				// 监听到用编辑器内容发生变化了，控制台打印编辑器的 html 内容...
				this.total = aiEditor.getText().length;
				// 定时生成大纲
				if (!this.T) {
					this.T = setTimeout(() => {
						this.proOutline();
						clearTimeout(this.T);
						this.T = null;
					}, 1000);
				}
				// 内容变化
				this.contentHtml = aiEditor.getHtml();
				this.$emit('changeTextHtml', aiEditor.getText());
				// 改变自动保存状态
				this.$emit('changeSaveSuccess', false);
				// 回答完成后内容发生变化自动保存
				if (this.done) {
					// 如果禁用自动保存就说明初始化的时候已经是保存状态
					if (this.disableSave) {
						this.$emit('changeSaveSuccess', true);
					}
					this.saveContent();
				}
			},
			link: {
				autolink: true,
				rel: 'custom-doc',
				class: '',
				bubbleMenuItems: ['visit']
			},
			textSelectionBubbleMenu: {
				enable: true,
				items: [
					{
						id: 'rewrite',
						icon:
							"<div style='line-height: 20px;font-size: 14px;color:#2F446B;'><i class='coos-iconfont icon-kuoxie' style='font-size:15px;" +
							"margin-right:4px;'></i><span>改写</span></div>",
						onClick: () => {
							this.handleAi('rewrite', 'icon-kuoxie');
						}
					},
					{
						id: 'extend',
						icon:
							"<div style='line-height: 20px;font-size: 14px;color:#2F446B;'><i class='coos-iconfont icon-xuxie1' style='font-size:15px;" +
							"margin-right:4px;'></i><span>续写</span></div>",
						onClick: () => {
							this.handleAi('extend', 'icon-xuxie1');
						}
					},
					{
						id: 'polish',
						icon:
							"<div style='line-height: 20px;font-size: 14px;color:#2F446B;'><i class='coos-iconfont icon-runse' style='font-size:15px;" +
							"margin-right:4px;'></i><span>润色</span></div>",
						onClick: () => {
							this.handleAi('polish', 'icon-runse');
						}
					},
					{
						id: 'overwrite',
						icon:
							"<div style='line-height: 20px;font-size: 14px;color:#2F446B;'><i class='coos-iconfont icon-zhongxie' style='font-size:15px;" +
							"margin-right:4px;'></i><span>重写</span></div>",
						onClick: () => {
							this.handleAi('overwrite', 'icon-zhongxie');
						}
					},
					{
						id: 'proofreadNum',
						icon:
							"<div style='line-height: 20px;font-size: 14px;color:#2F446B;'><i class='coos-iconfont icon-zhuanxie' style='font-size:15px;" +
							"margin-right:4px;'></i><span>校正</span></div>",
						onClick: () => {
							this.handleAi('proofreadNum', 'icon-zhuanxie');
						}
					}
				]
			},
			toolbarKeys: [
				exportBtn,
				'|',
				// {
				// 	icon: "<div style='font-size:14px;font-weight: 600;color:#15224c'>插入</div>",
				// 	toolbarKeys: [
				// 		'link',
				// 		'image',
				// 		'video',
				// 		'attachment',
				// 		'quote',
				// 		'code-block',
				// 		'table',
				// 		'source-code'
				// 	]
				// },
				// '|',
				'undo',
				'redo',
				'brush',
				// {
				// 	id: 'custom-brush',
				// 	tip: '格式刷',
				// 	icon: "<i class='coos-iconfont icon-runse'></i>",
				// 	onClick: () => {
				// 		this.brush();
				// 	}
				// },
				'eraser',
				'heading',
				'|',
				'font-family',
				'font-size',
				'bold',
				'italic',
				'underline',
				'strike',
				'highlight',
				'font-color',
				'align',
				'|',
				{
					id: 'outline',
					title: '大纲',
					icon: "<div style='font-size: 15px;color:#15224C'>大纲</div>",
					onClick: () => {
						this.openOutlinePopup();
					}
				}
				// {
				// 	toolbarKeys: [
				// 		'code',
				// 		'hr',
				// 		'line-height',
				// 		'subscript',
				// 		'superscript',
				// 		'todo',
				// 		'emoji',
				// 		'bullet-list',
				// 		'ordered-list',
				// 		'indent-decrease',
				// 		'indent-increase',
				// 		'break'
				// 	]
				// },
				// '|',
				// 'fullscreen'
			],
			onSave: editor => {
				this.saveContentNow();
			},
			fontSize: {
				defaultValue: 21.3,
				values: [
					{ name: '初号', value: 56 },
					{ name: '小初', value: 48 },
					{ name: '一号', value: 34.7 },
					{ name: '小一', value: 32 },
					{ name: '二号', value: 29.3 },
					{ name: '小二', value: 24 },
					{ name: '三号', value: 21.3 },
					{ name: '小三', value: 20 },
					{ name: '四号', value: 18.7 },
					{ name: '小四', value: 16 },
					{ name: '五号', value: 14 },
					{ name: '小五', value: 12 }
				]
			},
			fontFamily: {
				values: [
					{ name: '方正小标宋简体', value: '方正小标宋简体, 宋体, sans-serif' },
					{
						name: '黑体',
						value: '黑体, sans-serif'
					},
					{
						name: '楷体_GB2312',
						value: '楷体_GB2312, KaiTi_GB2312, 楷体, 楷体简, sans-serif'
					},
					{
						name: '仿宋_GB2312',
						value: '仿宋_GB2312, FangSong_GB2312, FangSong, 仿宋, STFangsong, sans-serif'
					},
					{ name: 'TimesNewRoman', value: 'Times New Roman' }
				]
			}
		});
	},
	methods: {
		/**点击的时候暂时生成*/
		mousedown() {
			// 点击事件处理完了再继续
			if (!this.done) {
				this.tempPause = true;
				setTimeout(() => {
					this.tempPause = false;
				}, 500);
			}
		},
		/**监听文章滚动*/
		scroll() {
			// 如果是滚动就不及时赋值改变数据
			if (
				this.$refs.scrollContent.scrollTop + this.$refs.scrollContent.clientHeight + 200 >=
				this.$refs.scrollContent.scrollHeight
			) {
				this.isBottom = true;
			} else if (!this.done) {
				this.isBottom = false;
			}
		},
		/**鼠标点击完毕事件*/
		mouseup() {
			// 下面自定义格式刷的不会被触发
			if (isEmpty(this.brushStyle)) return;
			this.aiEditor.blur();
			setTimeout(() => {
				let el = document.getElementsByClassName('selection-marker')[0];
				// 格式刷样式   并且  选中了文本    =   格式刷
				if (el) {
					this.brushToEl(el, this.brushStyle);
				}
				this.brushStyle = null;
				this.focus();
			}, 500);
		},
		/**格式刷获取选中文本的样式*/
		getPStyles(startNode) {
			// 从指定节点向上查找最近的p标签
			let pElement = startNode;
			while (pElement && pElement.tagName !== 'P') {
				pElement = pElement.parentElement;
				if (!pElement) break; // 如果到达文档根节点仍未找到p标签，则退出
			}

			if (!pElement) {
				console.warn('未找到p标签');
				return {
					pStyle: null,
					firstChildStyle: null
				};
			}

			// 获取p标签的style值
			const pStyle = pElement.getAttribute('style') || '';

			// 获取p标签的第一个子元素的style值
			let firstChildStyle = '';
			if (pElement.firstElementChild) {
				firstChildStyle = pElement.firstElementChild.getAttribute('style') || '';
			}
			return {
				pStyle: pStyle,
				firstChildStyle: firstChildStyle
			};
		},
		/**赋值给另外的元素*/
		brushToEl(dom, stylesObj) {
			// 1. 从指定节点向上查找最近的 p 标签
			let pElement = dom;
			while (pElement && pElement.tagName !== 'P') {
				pElement = pElement.parentElement;
				if (!pElement) break; // 如果到达根节点仍未找到 p 标签，则退出
			}

			if (!pElement) {
				console.warn('未找到 p 标签');
				return false; // 返回 false 表示未找到 p 标签
			}

			// 2. 将 stylesObj.pStyle 赋值给 p 标签的 style 属性
			if (stylesObj.pStyle) {
				console.log('设置p标签----', pElement);
				pElement.setAttribute('style', stylesObj.pStyle);
			} else {
				pElement.removeAttribute('style'); // 如果 pStyle 为空，则移除 style 属性
			}

			// 3. 如果 p 标签有第一个子元素，则赋值 firstChildStyle
			if (pElement.firstElementChild && stylesObj.firstChildStyle) {
				pElement.firstElementChild.setAttribute('style', stylesObj.firstChildStyle);
			} else if (pElement.firstElementChild && !stylesObj.firstChildStyle) {
				pElement.firstElementChild.removeAttribute('style'); // 如果 firstChildStyle 为空，则移除 style 属性
			}

			return true; // 返回 true 表示样式应用成功
		},
		/**格式刷*/
		brush() {
			// 格式刷选中的内容
			let el = document.getElementsByClassName('selection-marker')[0];
			if (el) {
				this.brushStyle = this.getPStyles(el);
				this.$nextTick(() => {
					this.focus();
				});
			}
		},
		/**寻找大纲标签*/
		findPreviousHeading(element) {
			let current = element;

			// 向上查找直到p标签或h标签
			while (current) {
				const tagName = current.tagName.toLowerCase();

				// 如果找到h标签，直接返回
				if (/^h[1-6]$/.test(tagName)) {
					return current;
				}

				// 如果找到p标签，开始向前查找h标签
				if (tagName === 'p') {
					let sibling = current.previousElementSibling;
					while (sibling) {
						const siblingTag = sibling.tagName.toLowerCase();
						if (/^h[1-6]$/.test(siblingTag)) {
							return sibling;
						}
						sibling = sibling.previousElementSibling;
					}
					// 如果p标签前没有h标签，继续向上查找
				}

				current = current.parentElement;
			}

			return null; // 没有找到任何符合条件的h标签
		},
		// 纠错导出
		errorExport() {
			let html = this.contentHtml;
			// 去掉span标签内只有mark标签的情况
			html = html.replace(/<span[^>]*>(<mark[^>]*>.*?<\/mark>)<\/span>/gi, '$1');
			// 去掉剩余的所有mark标签，保留其内容
			html = html.replace(/<mark[^>]*>(.*?)<\/mark>/gi, '$1');

			downloadFile({
				url: '/api/robot/writeProofread/v2/export',
				params: {
					html: html,
					id: this.id,
					title: this.docTitle
				},
				reqFn: true,
				success: () => {
					this.$message.success('导出成功！');
				},
				fail: error => {
					this.$message.error(error);
				}
			});
		},
		/**导出*/
		export(type) {
			if (!this.done) {
				this.$message.warning('请耐心等待文章生成完毕');
				return;
			}
			if (!this.id) {
				this.$message.warning('当前文章不可操作！');
				return;
			}
			downloadFile({
				url: '/api/robot/writeManuscript/export',
				params: {
					type,
					// html: '',
					id: this.id,
					title: this.docTitle
				},
				reqFn: true,
				success: () => {
					this.$message.success('导出成功！');
				},
				fail: error => {
					this.$message.error(error);
				}
			});
		},
		/**保存全文*/
		saveContent: debounce(
			function () {
				// 自动保存的时候判断  id + 当前页面 = 自动保存
				if (!this.id || this.current !== 3) return;
				// 禁止自动保存
				if (this.disableSave) {
					this.disableSave = false;
					return;
				}
				saveOutline({ saveType: 'manuscript', content: this.aiEditor.getHtml() }, this.id).then(
					res => {
						if (res.code === 200) {
							// TODO 保存全文内容成功后的操作
							this.$emit('changeSaveSuccess', true);
						} else {
							this.$message.error(res.message);
						}
					}
				);
			},
			5000,
			false
		),
		/**立即保存全文*/
		saveContentNow() {
			if (!this.id || !this.done) return;
			saveOutline({ saveType: 'manuscript', content: this.aiEditor.getHtml() }, this.id).then(
				res => {
					if (res.code === 200) {
						// TODO 保存全文内容成功后的操作
						this.$emit('changeSaveSuccess', true);
					} else {
						this.$message.error(res.message);
					}
				}
			);
		},
		/**聚焦*/
		focus() {
			if (!this.aiEditor.isFocused()) {
				this.aiEditor.focus();
			}
		},
		/**复制*/
		copy() {
			if (!this.done || !this.total) return;
			copyText(this.aiEditor.getText());
			this.$message.success('复制成功！');
		},
		/**设置大纲标题*/
		openSetting(item) {
			this.currentOutline = item;
			this.$emit('openSetting', item);
		},
		/**更新大纲(供外部调用)*/
		save(setting) {
			this.outline.forEach((item, index) => {
				if (item.id === this.currentOutline.id) {
					Object.keys(setting).forEach(key => {
						if (setting[key]) {
							item[key] = setting[key];
						}
					});
				}
			});
			this.$emit(
				'saveOutline',
				this.outline.map(item => {
					return { ...item, id: item.businessId };
				})
			);
		},
		/**重置*/
		reset() {
			this.isBottom = true;
			this.tempOutline = [];
			this.openOutline = false;
		},
		/**打开大纲*/
		openOutlinePopup() {
			this.openOutline = !this.openOutline;
			this.$emit('closeOther');
		},
		/**定位到目录位置*/
		toPos(item) {
			if (this.done) {
				this.aiEditor.focusPos(item.pos);
			}
		},
		/**生成大纲*/
		proOutline() {
			// 回答完成
			if (this.done) {
				// 第一次调用生成大纲方法，就改变状态，不生成综合大纲，避免onChange中也在调用生成大纲的方法
				if (this.doneFirstOutline) {
					this.doneFirstOutline = false;
				} else {
					this.finallyOutline();
				}
			}
			// 未回答完成，生成临时大纲
			else {
				// 回答过程中=》实时更新信源
				this.updateSourceInfo(true);
				this.tempOutline = this.aiEditor.getOutline();
			}
		},
		/**更新信源*/
		updateSourceInfo(pending) {
			// 主要针对content发生变化，触发更新信源，但是还未获取到id
			if (!this.id) return;
			this.$emit('updateSourceInfo', pending);
		},
		/**保存默认标题*/
		saveTitle() {
			if (this.id && !this.docTitle && !this.saveTitleStatus && this.tempOutline.length > 0) {
				this.saveTitleStatus = true;
				saveOutline(
					{
						saveType: 'title',
						title: this.tempOutline[0].text
					},
					this.id
				).then(res => {
					if (res.code === 200) {
						this.saveTitleStatus = false;
						this.$emit('changeTitle', this.tempOutline[0].text);
					} else {
						this.$message.error(res.message);
					}
				});
			}
		},
		finallyOutline() {
			// 新生成的大纲
			let outline = this.aiEditor.getOutline();
			// 整合大纲，以新的为主，旧的为辅
			this.outline = outline.map((item, index) => {
				let obj = this.initOutline.find(m => m.title === item.text);
				return {
					...obj,
					businessId: obj ? obj.id : '',
					...item,
					title: obj ? obj.title : item.text,
					level: obj ? obj.level : item.level - 1 // 和后端生成的同步
				};
			});
		},
		/**清除编辑器内容*/
		clear() {
			this.disableSave = true;
			this.pauseStatus = false;
			// this.aiEditor.clear();
		},
		/**暂停*/
		pause() {
			this.pauseStatus = true;
			this.$emit('pause');
		},
		/**Ai相关操作*/
		handleAi(type, icon) {
			// if (this.pauseStatus) {
			// 	this.$message.warning('生成暂停，无法操作！');
			// 	return;
			// }
			let el = document.getElementsByClassName('selection-marker')[0];
			let currentNode = this.findPreviousHeading(el);
			let obj = this.outline.find(item => {
				return item.id === currentNode.id;
			});
			this.$emit('aiQuery', obj || {}, {
				type,
				icon,
				selectContent: this.aiEditor.getSelectedText()
			});
		},
		/**替换*/
		replace(type, arr) {
			// 获取选中节点的样式
			let dom = document.getElementsByClassName('selection-marker')[0];
			// 如果是插入，并且有选中，需要将选中内容一并重新渲染
			if (type === 'insert') {
				this.focus(); // 自动聚焦上次失焦的位置
				// 判断是否有选中的内容
				let selectContent = this.aiEditor.getSelectedText();
				if (selectContent) {
					arr.unshift({
						type: 'html',
						content: selectContent
					});
				}
			} else if (!dom) {
				this.$message.warning('请选中需要替换的内容！');
				return;
			}
			arr.forEach(item => {
				if (item.type === 'html') {
					this.aiEditor.focus().insert(item.content.replace(/[#*]/gi, ''));
				} else if (item.type === 'md') {
					this.aiEditor.focus().insertMarkdown(item.content);
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.editor-content {
	display: flex;
	height: 100%;
	width: 100%;
	flex-direction: column;
}
.editor-top {
	flex: 1;
	overflow: hidden;
	display: flex;
}

.ai-helper-center {
	height: 100%;
	width: 100%;
	position: relative;
	flex: 1;
}

.aie-header-panel {
	position: sticky;
	z-index: 1;
}

.aie-container {
	border: none !important;
}

.aie-main {
	position: relative;
	flex: 1;
	display: flex;
	background: #ffffff;
	overflow: hidden;
}
.aie-main-content {
	flex: 1;
	display: flex;
	justify-content: center;
}
.aie-container-panel {
	position: relative;
	background-color: #ffffff;
	height: 100%;
	width: 70%;
	max-width: 764px;
}
// 因为插件默认盒子模型为内容，这里修改默认
.aie-container * {
	box-sizing: border-box;
}
.aie-container-main {
	//padding: 0 52px;
	overflow: auto;
	height: 100%;
	&::-webkit-scrollbar {
		display: none;
	}
}
.footer {
	background: #ffffff;
	width: 100%;
	padding: 0 16px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1px solid #e3ebf2;
	.number {
		font-weight: 400;
		font-size: 14px;
		color: #15224c;
		line-height: 22px;
	}
}
// 让header居中
::v-deep .aie-header-panel aie-header > div {
	align-items: center;
	justify-content: center !important;
	border: none;
	padding: 23px 0;
}
::v-deep .aie-container-footer aie-footer > div {
	display: none !important;
}
::v-deep .el-textarea__inner {
	box-sizing: border-box;
}
.cover {
	width: 24px;
	height: 24px;
}
.pending {
	position: absolute;
	bottom: 24px;
	width: 95%;
	left: 2.5%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: $borderRadius;
	box-shadow: 0px 8px 10px rgba(0, 0, 0, 0.08), 0px 16px 24px rgba(0, 0, 0, 0.04);
	background: #ffffff;
	border: 1px solid $borderColor;
	padding: 12px;
	z-index: 670;
}
.pending-left {
	display: flex;
	align-items: center;
	font-size: 14px;
}
.pending-icon {
	color: #ff7a7b;
	font-size: 16px;
	margin-right: 4px;
}
.loading-icon {
	font-size: 16px;
	margin-right: 12px;
	color: var(--brand-6);
}

.close-outline-icon {
	font-size: 14px;
	cursor: pointer;
	color: #737a94;
}
.doc-icon {
	font-size: 60px;
	color: $holderTextColor;
	position: absolute;
	z-index: 666;
}
.top-left {
	transform: rotateZ(180deg);
	top: 12px;
	left: 12px;
}
.top-right {
	transform: rotateZ(-90deg);
	top: 12px;
	right: 12px;
}
.bottom-right {
	bottom: 12px;
	right: 12px;
}
.bottom-left {
	transform: rotateZ(90deg);
	bottom: 12px;
	left: 12px;
}
.outline {
	flex-shrink: 0;
	width: 330px;
	height: 100%;
	background: #ffffff;
	z-index: 669;
	display: flex;
	flex-direction: column;
	border-right: 1px solid #e3ebf2;
	&-title {
		padding: 20px;
		font-weight: 600;
		font-size: 18px;
		color: #15224c;
		line-height: 21px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #e3ebf2;
	}
}
.outline-content {
	flex: 1;
	overflow: auto;
	padding: 20px;
	&-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		cursor: pointer;
		border-radius: $borderRadius;
		margin-bottom: 15px;
		height: 22px;
		.setting {
			display: none;
			color: $holderTextColor;
			font-size: 16px;
			cursor: pointer;
			margin-left: 4px;
		}
		&-left {
			flex: 1;
			display: flex;
			align-items: center;
			overflow: hidden;
			&-text {
				font-weight: 400;
				font-size: 14px;
				color: #2f446b;
				line-height: 16px;
				display: flex;
				align-items: center;
				flex: 1;
				overflow: hidden;
				.label-text {
					flex: 1;
					margin-right: 8px;
					@include aLineEllipse;
				}
			}
		}
		&:hover {
			.setting {
				display: block;
			}
		}
	}
}
::v-deep .aie-container .aie-bubble-menu .active {
	background: transparent !important;
}
.copy {
	font-weight: 400;
	font-size: 14px;
	color: #737a94;
	line-height: 22px;
	margin-left: 4px;
	cursor: pointer;
}
.footer-icon {
	font-size: 16px;
	color: #737a94;
}
.tag-content {
	display: flex;
	padding: 0 6px;
	font-weight: 400;
	font-size: 14px;
	color: #2591f7;
	line-height: 22px;
	background: #ecf5ff;
	border-radius: 3px;
}
.tag-icon {
	font-size: 12px;
	color: #2591f7;
	margin-right: 6px;
}
.outline-docTitle {
	text-align: center;
	font-size: 16px;
	font-weight: 600;
}
::v-deep .tableWrapper {
	& table {
		border-radius: 6px;
		& tr:first-child {
			background: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 100%, #ffffff 100%);
		}
		& th {
			background: transparent;
		}
		& tr {
			border-top: 1px solid #dfe2e5;
		}
		& th,
		td {
			border: 1px solid #dfe2e5;
		}
	}
}
::v-deep .aie-content {
	font-size: 21.3px; // 默认字号
	font-family: 仿宋_GB2312, FangSong_GB2312, FangSong, 仿宋, STFangsong, sans-serif; // 默认字体
	// 解决格式刷的问题的，有点难搞，暂时写死
	p {
		line-height: 29.5pt;
		text-indent: 2em;
	}
}
.disable {
	cursor: not-allowed;
}
.process {
	height: 100%;
	border-radius: $borderRadius;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background: rgba(80, 248, 2, 0.1);
}
// 只读模式导出
/* 使用深度选择器穿透 scoped 限制 */
::v-deep .aie-menu-item:has(.export-class) {
	opacity: 1 !important;
	pointer-events: auto !important;
}
</style>
<style>
mark[data-color='#ffffff'] {
	cursor: pointer;
	text-decoration: underline;
}
</style>
