export default {
	methods: {
		/**
		 * 处理标记
		 * @param {Array} items - 项目数组
		 * @param {Function} matchFn - 匹配函数
		 * @param {Function} styleFn - 样式函数
		 * @param {string|Function} replacedText - 替换文本
		 */
		processMarks(
			items,
			matchFn,
			styleFn = () => ({ color: '#e34d59', backgroundColor: '#ffffff', textColor: '#e34d59' }),
			replacedText = ''
		) {
			const parser = new DOMParser();
			const doc = parser.parseFromString(this.content, 'text/html');
			const targetTexts = new Set(
				items
					.filter(item => matchFn(item, item.originalContentText))
					.map(item => item.originalContentText)
			);
			doc.querySelectorAll('span').forEach(el => {
				const markEl = el.querySelector('mark');
				if (markEl) {
					const text = markEl.textContent;
					if (targetTexts.has(text)) {
						const item = items.find(i => i.originalContentText === text);
						const { color, backgroundColor, textColor } = styleFn(item);
						const newSpan = document.createElement('span');
						newSpan.style.color = color;
						const newMark = document.createElement('mark');
						newMark.setAttribute('data-color', backgroundColor);
						newMark.style.background = backgroundColor;
						newMark.style.color = textColor;
						newMark.textContent =
							typeof replacedText === 'function' ? replacedText(item) : replacedText || text;
						newSpan.appendChild(newMark);
						el.replaceWith(newSpan);
						// const index = this.findTextIndexInHTML(text, textNodes, currentIndex);
						// if (index !== -1) {
						// 	currentIndex = index;
						// }
					}
				}
			});
			this.content = doc.body.innerHTML;
			this.$emit('setErrorContent', doc.body.innerHTML, this.contentIndex);
			// this.contentIndex = 0;
		},

		/**
		 * 设置错误列表
		 * @param {Array} listArr - 错误列表数组
		 */
		setErrorList(listArr) {
			const removeQuotes = str => str.replace(/^“|”$/g, '');
			this.errorList = listArr.map((item, index) => {
				const processContentText = (content, item) => {
					let originalContentText = removeQuotes(item.errorWord);
					let correctedContentText = removeQuotes(item.rightWord);

					const replaceQuotes = text => text.replace(/‘/g, '“').replace(/’/g, '”');
					if (!content.includes(originalContentText)) {
						originalContentText = replaceQuotes(originalContentText);
						correctedContentText = replaceQuotes(correctedContentText);
					}
					return {
						originalContentText,
						correctedContentText
					};
				};
				const contentText = processContentText(this.content, item);
				return {
					...item,
					originalContent: contentText.originalContentText,
					correctedContent: contentText.correctedContentText,
					errorType: item.errorType,
					originalContentText:
						item.dealStatus === 'replaced'
							? contentText.correctedContentText
							: contentText.originalContentText,
					correctedContentText:
						item.dealStatus === 'replaced'
							? contentText.originalContentText
							: contentText.correctedContentText,
					errorTypeStyle:
						item.errorType === '常识错误'
							? 'common'
							: item.errorType === '用词不当'
							? 'spelling'
							: 'semantic',
					isChecked: false
				};
			});
			this.setErrorTextFun(this.errorList);
		},

		/**
		 * 替换并采用处理
		 * @param {string} dealStatus - 最后处理结果
		 */
		replaceAndAdopt(dealStatus) {
			const checkedItems = this.errorList.filter(item => item.isChecked);
			checkedItems.forEach(item => {
				if (dealStatus === 'replaced') {
					item.originalContentText = item.originalContent;
					item.correctedContentText = item.correctedContent;
				} else {
					item.originalContentText = item.originalContent;
					item.correctedContentText = item.correctedContent;
				}
				item.dealStatus = dealStatus === 'replaced' ? 'untreated' : 'replaced';
			});
			this.errorList.forEach(item => {
				if (item.isChecked) {
					item.isChecked = false;
				}
			});
			const stack = checkedItems.map(item => ({
				oldText: item.originalContent,
				newText: item.correctedContent
			}));
			this.replacedList = stack;
			// this.updateDealResult(stack);
			this.replaceAndAdoptFun(checkedItems, dealStatus);
			this.dealStatus = '';
		},

		/**
		 * 替换并采用功能
		 * @param {Array} arr - 项目数组
		 * @param {string} dealStatus - 最后处理结果
		 */
		replaceAndAdoptFun(arr, dealStatus) {
			if (dealStatus === 'replaced') {
				this.processMarks(
					arr,
					(item, text) => item.originalContentText === text,
					() => ({ color: '#e34d59', backgroundColor: '#ffffff', textColor: '#e34d59' }),
					item => item.correctedContentText
				);
			} else {
				this.processMarks(
					arr,
					(item, text) => item.originalContentText === text,
					() => ({ color: '#00bb00', backgroundColor: '#ffffff', textColor: '#ffffff' }),
					item => item.correctedContentText
				);
			}
		},

		/**
		 * 用段落标签包装内容
		 * @param {string} content - 内容
		 * @returns {string} - 包装后的内容
		 */
		wrapWithParagraphTags(content) {
			if (!content) return '';
			const startsWithHtmlTag = /^<([a-z]+)(\s+[^>]*)?>/i.test(content);
			if (startsWithHtmlTag) {
				return content;
			}
			const paragraphs = content.split(/\n\s*\n/);
			return paragraphs.map(p => `<p>${p.replace(/\n/g, '<br>')}</p>`).join('');
		},

		/**
		 * 选择错误文本
		 * @param {Array} arr - 选中的项目数组
		 * @param {Array} newArr - 新的项目数组
		 */
		selectErrorText(arr, newArr) {
			arr = Array.isArray(arr) ? arr : [];
			newArr = Array.isArray(newArr) ? newArr : [];
			const config = {
				untreated: {
					color: '#e34d59',
					backgroundColor: '#ffffff',
					textColor: '#e34d59'
				},
				replaced: {
					color: '#00bb00',
					backgroundColor: '#ffffff',
					textColor: '#00bb00'
				},
				ignored: {
					color: '#ED7B2F',
					backgroundColor: '#ffffff',
					textColor: '#ED7B2F'
				}
			};

			const newGroups = { untreated: [], replaced: [], ignored: [] };
			newArr.forEach(item => {
				if (item.dealStatus in newGroups) {
					newGroups[item.dealStatus].push(item);
				}
			});
			Object.entries(newGroups).forEach(([key, items]) => {
				this.processMarks(
					items,
					(item, text) => item.originalContentText === text,
					() => config[key]
				);
			});
			// const selectedItems = arr.filter(item => item.dealStatus !== 'replaced');
			this.processMarks(
				arr,
				(item, text) => item.originalContentText === text,
				() => ({ color: '#15224C', backgroundColor: '#ec9191', textColor: '#15224C' })
			);
		},

		/**
		 * 设置错误文本功能
		 * @param {Array} arr - 项目数组
		 */
		setErrorTextFun(arr) {
			let content = this.oldContent;
			arr.forEach(item => {
				const regex = new RegExp(this.escapeRegExp(item.originalContentText), 'g');
				if (item.dealStatus === 'replaced') {
					content = content.replace(regex, match => {
						return `<span style="color: #00bb00;"><mark data-color="#ffffff" style="background:#ffffff; color: #00bb00;">${match}</mark></span>`;
					});
				} else {
					content = content.replace(regex, match => {
						return `<span style="color: #e34d59;"><mark data-color="#ffffff" style="background:#ffffff; color: #e34d59;">${match}</mark></span>`;
					});
				}
			});
			this.content = content;
			this.$emit('setErrorContent', content, 0);
		},

		/**
		 * 重置标记样式
		 */
		resetMarkStyle() {
			const parser = new DOMParser();
			const doc = parser.parseFromString(this.content, 'text/html');

			doc.querySelectorAll('span').forEach(el => {
				const markEl = el.querySelector('mark');
				if (markEl) {
					const text = markEl.textContent;
					const newSpan = document.createElement('span');
					newSpan.style.color = '#e34d59';
					const newMark = document.createElement('mark');
					newMark.setAttribute('data-color', '#ffffff');
					newMark.style.background = '#ffffff';
					newMark.style.color = '#e34d59';
					newMark.textContent = text;
					newSpan.appendChild(newMark);
					el.replaceWith(newSpan);
				}
			});

			this.content = doc.body.innerHTML;
		},
		/**
		 * 转义正则表达式特殊字符
		 * @param {string} string - 字符串
		 * @returns {string} - 转义后的字符串
		 */
		escapeRegExp(string) {
			return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
		},

		/**
		 * 获取文本节点
		 * @param {Node} node - 节点
		 * @returns {Array} - 文本节点数组
		 */
		getTextNodesIn(node) {
			const textNodes = [];
			const walker = document.createTreeWalker(node, NodeFilter.SHOW_TEXT, null, false);
			let currentNode;
			while ((currentNode = walker.nextNode())) {
				textNodes.push(currentNode);
			}
			return textNodes;
		},
		/**
		 * 忽略选中项功能
		 * @param {Array} arr - 项目数组
		 */
		ignoreSelectedFun(arr) {
			if (arr.length === 0) {
				return;
			}
			this.processMarks(
				arr.filter(item => item.dealStatus === 'ignored'),
				(item, text) => item.originalContentText === text,
				() => ({ color: '#ED7B2F', backgroundColor: '#ffffff', textColor: '#ED7B2F' })
			);
		},

		/**
		 * 忽略选中项
		 */
		ignoreSelected() {
			const checkedItems = this.errorList.filter(item => item.isChecked);
			checkedItems.forEach(item => {
				item.dealStatus = 'ignored';
			});
			this.updateDealResult();
			this.ignoreSelectedFun(checkedItems);
			this.errorList.forEach(item => {
				if (item.isChecked) {
					item.isChecked = false;
				}
			});
			this.dealStatus = '';
		}
	}
};
