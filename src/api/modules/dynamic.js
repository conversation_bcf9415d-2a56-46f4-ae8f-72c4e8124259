// 获取部门树
import request from '@/utils/request';

export function getDepartmentTree(params) {
	return request({
		url: '/api/sys/organization/tree',
		method: 'get',
		params
	});
}
// 岗位列表
export function getPostPage(params) {
	return request({
		url: `/api/sys/post/page`,
		params,
		method: 'get'
	});
}
/**获取动态列表设计*/
export function getDesigns(params) {
	return request({
		url: `/api/form/sys/designs/search`,
		method: 'get',
		params
	});
}
export function getDepartsChildren(params) {
	return request({
		url: `/api/sys/departs/children/${params.pid}`,
		method: 'get'
	});
}
export function addDirectors(id, data) {
	return request({
		url: `/api/sys/depart/${id}/directors`,
		method: 'PUT',
		data
	});
}
export function getDepartsRootSearch(params) {
	return request({
		url: `/api/sys/departs/root/search`,
		method: 'post',
		data: params
	});
}
export function addDepart(data) {
	return request({
		url: `/api/sys/depart`,
		method: 'post',
		data: data
	});
}
export function getDepart(params) {
	return request({
		url: `/api/sys/depart/${params.id}`,
		method: 'get'
	});
}
export function editDepart(data) {
	return request({
		url: `/api/sys/depart/${data.id}`,
		method: 'put',
		data: data.data
	});
}
// 删除部门
export function delDepart(ids) {
	return request({
		url: `/api/sys/depart/${ids}`,
		method: 'delete'
	});
}
// 岗位树
export function treeByDept(params) {
	return request({
		url: `/api/sys/post/treeByDept`,
		method: 'get',
		params
	});
}
export function getorgAndDepartTree(params) {
	return request({
		url: `/api/sys/depart/orgAndDepartTree`,
		method: 'get',
		params
	});
}
// 删除成员
export function deleteTenantUser(ids) {
	return request({
		url: `/api/sys/tenant/user/${ids}`,
		method: 'delete'
	});
}
export function getUserRole(params) {
	return request({
		url: `/api/sys/tenant/user/${params.id}`,
		method: 'get'
	});
}
export function getUsers(params) {
	return request({
		url: `/api/sys/tenant/users`,
		method: 'post',
		data: params
	});
}
export function tenantUser(data) {
	return request({
		url: `/api/sys/tenant/user`,
		method: 'post',
		data
	});
}
// 修改成员信息
export function updateTenantUser(data, id) {
	return request({
		url: `/api/sys/tenant/user/${id}`,
		method: 'put',
		data
	});
}
