<template>
	<div class="main-content">
		<div v-if="comprehensiveList.length">
			<div v-for="item in comprehensiveList" :key="item.accessName" class="content">
				<div>
					<div class="content-title">{{ item.accessName }}</div>
					<div v-for="(item1, index1) in item.records" :key="index1">
						<div v-if="index1 < 3" class="content-list" @click="handleSearch(item1)">
							<el-image
								v-if="/http/.test(item1.icon)"
								:src="item1.icon"
								class="content-list-img"
							></el-image>
							<i
								v-if="item1.icon == 'icon-shop'"
								class="coos-iconfont icon-shop"
								style="font-size: 40px"
							></i>
							<svg-icon
								v-if="iconType.includes(item1.icon)"
								class="content-list-img"
								style="width: 40px; height: 40px"
								:icon-class="item1.icon"
							></svg-icon>
							<div v-if="item1.icon == '#last'" class="img">
								{{ item1.title.charAt(item1.title.length - 1) }}
							</div>
							<div v-if="item1.icon == '#first'" class="img">{{ item1.title.charAt(0) }}</div>
							<div class="content-list-info">
								<div class="content-list-info-title">
									<span
										v-for="(event, index) in getHilightStrArray(item1.title, keywords)"
										:key="index"
										class="title-class-text"
										:style="event == keywords ? 'color:#0f45ea' : ''"
									>
										{{ event }}
									</span>
								</div>
								<div class="content-list-info-msg">
									<span
										v-for="(event, index) in getHilightStrArray(item1.summary, keywords)"
										:key="index"
										class="summary-text"
										:style="event == keywords ? 'color:#0f45ea' : ''"
									>
										{{ event }}
									</span>
								</div>
								<div
									v-if="item1.footer"
									class="content-list-info-msg"
									v-html="item1.footer.join('&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp')"
								></div>
							</div>
						</div>
					</div>
					<div class="content-more" @click="goAppointSearch(item.accessName, item.accessId)">
						在
						<span class="content-more-tabs">{{ item.accessName }}</span>
						中搜索全部
						<i class="el-icon-arrow-right"></i>
					</div>
				</div>
			</div>
		</div>
		<div v-if="!comprehensiveList.length && !keywords" class="empty">
			<img
				style="width: 144px; height: 144px"
				:src="require(`../../../assets/${rentThem}/home/<USER>"
			/>
			<div class="prompt">你好，欢迎来到智能搜索页</div>
		</div>
		<div v-if="!comprehensiveList.length && keywords" class="empty">
			<BasicEmpty :data="comprehensiveList" name="no-search" />
		</div>
		<!-- 待办 -->
		<waitDetail ref="waitDetail"></waitDetail>
	</div>
</template>

<script>
import waitDetail from '@/components/wait-detail/index.vue';
import { mapState } from 'vuex';
import { preUrl } from '@/config';

export default {
	components: { waitDetail },
	props: {
		comprehensiveList: {
			type: Array,
			default: () => []
		},
		iconType: {
			type: Array,
			default: () => []
		},
		keywords: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	computed: {
		...mapState('user', ['waitConfig']),
		// 打开待办的方式
		openDetailType() {
			return this.waitConfig.customerTaskMenu
				? this.waitConfig.customerTaskMenu.detailOpenType
				: '1';
		}
	},
	// watch: {
	// 	getKeywords(val) {
	// 		console.log(val);
	// 		this.$emit('handleChoice', val);
	// 	}
	// },
	methods: {
		getHilightStrArray(str, key) {
			let arr = str.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
			arr = arr.filter(element => element !== '');
			return arr;
		},
		/**在指定分类搜索 */
		goAppointSearch(accessName, accessApplicationId) {
			this.$emit('handleChoice', accessName, accessApplicationId);
		},
		parseQueryString(url) {
			// 获取查询字符串部分
			const queryString = url.split('?')[1];
			// 将查询字符串分割成键值对
			const params = queryString.split('&');
			// 构建对象
			const result = {};
			params.forEach(param => {
				const [key, value] = param.split('=');
				result[key] = decodeURIComponent(value);
			});
			return result;
		},
		/** 查询 */
		handleSearch(item) {
			let { sourceUrl, openType, title, icon, isExternal } = item;
			this.$emit('beforeclose');
			try {
				if (openType == 1) {
					// 跳转内部
					let newUrl = '';
					if (isExternal) {
						sourceUrl = /http/gi.test(sourceUrl)
							? sourceUrl
							: (preUrl || window.location.origin) + sourceUrl;
					}
					// http 链接
					if (sourceUrl.indexOf('http') != -1) {
						let base = '/other-system';
						newUrl =
							base +
							'?url=' +
							encodeURIComponent(sourceUrl) +
							'&name=' +
							title +
							'&logoUrlPath=' +
							icon +
							'&isMenuTab=' +
							true;
					} else {
						newUrl = sourceUrl;
					}
					if (newUrl.indexOf('wait-handle') !== -1) {
						console.log('wait-handle');
						let row = this.parseQueryString(newUrl);
						if (this.openDetailType === '2') {
							const route = this.$router.resolve({ name: 'WaitDetail' });
							window.open(`${route.href}?blank_id=${row.id}`, '_blank');
						} else {
							this.$refs.waitDetail.openDetail(row.id);
						}
					} else {
						// 项目链接
						this.$router.push(newUrl);
					}
				} else {
					if (sourceUrl.indexOf('http') != -1) {
						// 跳转外部
						window.open(`${sourceUrl}`);
						return;
					}
					let url = window.location.origin + sourceUrl;
					window.open(`${url}`);
					// this.$message.error('请检查配置链接');
				}
			} catch (e) {
				console.log(e);
				this.$message.error('请检查配置链接');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main-content {
	padding-bottom: 72px;
}

.content {
	margin: 32px 22px;

	.img {
		width: 40px;
		height: 40px;
		background: var(--brand-6);
		border-radius: 6px;
		margin-right: 3px;
		color: #fff;
		text-align: center;
		line-height: 40px;
		flex-shrink: 0;
	}

	&-title {
		font-size: 16px;
		line-height: 22px;
		font-weight: 800;
		color: $primaryTextColor;
		margin-bottom: 23px;
	}

	&-list {
		display: flex;
		padding: 15px 8px;
		cursor: pointer;

		&-img {
			width: 40px;
			height: 40px;
			border-radius: 6px;
			flex-shrink: 0;
		}

		&-info {
			overflow: hidden;
			margin-left: 8px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			&-title {
				font-size: 14px;
				font-weight: 500;
				color: $primaryTextColor;
				line-height: 22px;
				@include aLineEllipse;
			}

			&-msg {
				font-size: 12px;
				font-weight: 400;
				color: $subTextColor;
				line-height: 20px;
				@include aLineEllipse;
			}
		}
	}

	&-more {
		cursor: pointer;
		text-align: center;
		color: $subTextColor;
		font-weight: 500;
		font-size: 14px;
		margin-top: 20px;
		padding-bottom: 12px;
		border-bottom: 1px solid #dce3e7;

		&-tabs {
			color: var(--brand-6);
		}
	}
}

.empty {
	margin-top: 80px;
	display: flex;
	flex-direction: column;
	align-items: center;

	.prompt {
		margin-top: 13px;
		font-weight: 600;
		color: $primaryTextColor;
		line-height: 28px;
		font-size: 20px;
	}
}

.title-class-text {
	font-weight: 500;
	font-size: 28rpx;
	color: $primaryTextColor;
}

.summary-text {
	font-weight: 400;
	font-size: 24rpx;
	color: #737a94;
}
</style>
