<template>
	<div class="document">
		<div v-if="categoryList.length || keywords">
			<div v-if="keyword" class="totalNum">
				{{ totalNum }}条与"
				<span class="keyword">{{ keyword }}</span>
				"相关的搜索结果
			</div>
			<div class="searchList">
				<div
					v-for="(item, index) in categoryList"
					:key="index"
					class="searchList-item"
					@click="handleSearch(item)"
				>
					<!-- <svg-icon icon-class="search-doc" class="icon"></svg-icon> -->

					<el-image v-if="item.icon.length > 10" :src="item.icon" class="icon"></el-image>
					<i
						v-if="item.icon == 'icon-shop'"
						class="coos-iconfont icon-shop"
						style="font-size: 40px"
					></i>
					<svg-icon
						v-if="iconType.includes(item.icon)"
						class="icon"
						:icon-class="item.icon"
					></svg-icon>
					<div v-if="'#last' == item.icon" class="img">
						{{ item.title.charAt(item.title.length - 1) }}
					</div>
					<div v-if="'#first' == item.icon" class="img">{{ item.title.charAt(0) }}</div>
					<div class="docInfo">
						<div class="docName">
							<span
								v-for="(event, eventI) in getHilightStrArray(item.title, keywords)"
								:key="'docName-' + eventI"
								class="title-class-text"
								:style="event == keywords ? 'color:#0f45ea' : ''"
							>
								{{ event }}
							</span>
						</div>
						<div class="docAbstract">
							<span
								v-for="(event, eventI) in getHilightStrArray(item.summary, keywords)"
								:key="'docAbstract-' + eventI"
								class="summary-text"
								:style="event == keywords ? 'color:#0f45ea' : ''"
							>
								{{ event }}
							</span>
						</div>
						<div class="docBy" v-html="item.footer.join('&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp')"></div>
					</div>
				</div>
			</div>
		</div>
		<div v-else class="empty">
			<img
				style="width: 144px; height: 144px"
				:src="require(`@/assets/${rentThem}/home/<USER>"
			/>
			<div class="prompt">你好，欢迎来到智能搜索页</div>
		</div>
		<!-- 待办 -->
		<waitDetail ref="waitDetail"></waitDetail>
	</div>
</template>

<script>
import waitDetail from '@/components/wait-detail/index.vue';
import { mapState } from 'vuex';
import { preUrl } from '@/config';

export default {
	components: { waitDetail },
	props: {
		keyword: {
			type: String,
			default: ''
		},
		categoryList: {
			type: Array,
			default: () => []
		},
		iconType: {
			type: Array,
			default: () => []
		},
		totalNum: {
			type: [Number, String],
			default: 0
		},
		keywords: {
			type: String,
			default: ''
		}
	},
	data() {
		return {};
	},
	computed: {
		...mapState('user', ['waitConfig']),
		// 打开待办的方式
		openDetailType() {
			return this.waitConfig.customerTaskMenu
				? this.waitConfig.customerTaskMenu.detailOpenType
				: '1';
		}
	},
	methods: {
		getHilightStrArray(str, key) {
			let arr = str.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
			arr = arr.filter(element => element !== '');
			return arr;
		},
		parseQueryString(url) {
			// 获取查询字符串部分
			const queryString = url.split('?')[1];
			// 将查询字符串分割成键值对
			const params = queryString.split('&');
			// 构建对象
			const result = {};
			params.forEach(param => {
				const [key, value] = param.split('=');
				result[key] = decodeURIComponent(value);
			});
			return result;
		},
		handleSearch(item) {
			let { sourceUrl, openType, title, icon, isExternal } = item;
			this.$emit('beforeclose');
			try {
				if (openType == 1) {
					// 跳转内部
					let newUrl = '';
					if (isExternal) {
						sourceUrl = /http/gi.test(sourceUrl)
							? sourceUrl
							: (preUrl || window.location.origin) + sourceUrl;
					}
					// http 链接
					if (sourceUrl.indexOf('http') != -1) {
						let base = '/other-system';
						newUrl =
							base +
							'?url=' +
							encodeURIComponent(sourceUrl) +
							'&name=' +
							title +
							'&logoUrlPath=' +
							icon +
							'&isMenuTab=' +
							true;
					} else {
						newUrl = sourceUrl;
					}
					// 项目链接
					if (newUrl.indexOf('wait-handle') !== -1) {
						console.log('wait-handle');
						let row = this.parseQueryString(newUrl);
						if (this.openDetailType === '2') {
							const route = this.$router.resolve({ name: 'WaitDetail' });
							window.open(`${route.href}?blank_id=${row.id}`, '_blank');
						} else {
							this.$refs.waitDetail.openDetail(row.id);
						}
					} else {
						// 项目链接
						this.$router.push(newUrl);
					}
				} else {
					if (sourceUrl.indexOf('http') != -1) {
						// 跳转外部
						window.open(`${sourceUrl}`);
						return;
					}
					let url = window.location.origin + sourceUrl;
					window.open(`${url}`);
				}
			} catch (e) {
				this.$message.error('请检查配置链接');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.document {
	padding: 19px 31px 36px 31px;
	.totalNum {
		font-size: 14px;
		font-weight: 500;
		color: $primaryTextColor;
		margin-bottom: 24px;
	}
	.keyword {
		color: var(--brand-6);
	}
	.searchList {
		margin-left: 7px;

		.searchList-item {
			padding: 12px 8px;
			border-bottom: 1px solid #f0f0f0;
			display: flex;
			cursor: pointer;
			.icon {
				width: 40px;
				height: 40px;
				flex-shrink: 0;
				border-radius: 6px;
			}
			.img {
				width: 40px;
				height: 40px;
				background: var(--brand-6);
				border-radius: 6px;
				//margin-right: 8px;
				color: #fff;
				text-align: center;
				line-height: 40px;
			}
			.docInfo {
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				font-size: 12px;
				font-weight: 400;
				margin-left: 8px;
				color: $subTextColor;
				overflow: hidden;
				line-height: 20px;
				.docAbstract {
					@include aLineEllipse;
				}
				.docName {
					font-weight: 500;
					font-size: 14px;
					line-height: 22px;
					color: $primaryTextColor;
					@include aLineEllipse;
				}
				.docBy {
					// letter-spacing: 3px;
					@include aLineEllipse;
					span {
						color: $primaryTextColor;
					}
				}
			}
		}
	}
	.empty {
		margin-top: 61px;
		display: flex;
		flex-direction: column;
		align-items: center;
		.prompt {
			margin-top: 13px;
			font-weight: 600;
			color: $primaryTextColor;
			line-height: 28px;
			font-size: 20px;
		}
	}
}

.title-class-text {
	font-weight: 500;
	font-size: 28rpx;
	color: $primaryTextColor;
}

.summary-text {
	font-weight: 400;
	font-size: 24rpx;
	color: #737a94;
}
</style>
